import { NextRequest, NextResponse } from 'next/server'

import { i18nMiddleware } from '@/middlewares/i18n'
import { authMiddleware } from '@/middlewares/auth'
import { aliasMiddleware } from '@/middlewares/alias'
import { queryMiddleware } from '@/middlewares/query'

import { defineApi } from '@/utils/defineApi'

export const config = {
  matcher: [
    '/((?!api|_next/static|_next/image|images|assets|favicon.ico|sw.js|safe-padding.js|manifest|icons|logout|image|static|audio).*)',
  ],
}

export type Middleware = (
  req: NextRequest,
  res: NextResponse,
  next: () => Promise<NextResponse>,
) => Promise<NextResponse>

defineApi({
  getErrorFunc: async (response) => response,
})

export function middleware(request: NextRequest) {
  const response = NextResponse.next()

  // 添加 CORS 頭
  response.headers.set('Access-Control-Allow-Origin', '*')
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization')

  const middlewares: Middleware[] = [aliasMiddleware, i18nMiddleware, authMiddleware, queryMiddleware]

  return executeMiddlewares(middlewares, request, response)
}

async function executeMiddlewares(
  middlewares: Middleware[],
  req: NextRequest,
  res: NextResponse,
): Promise<NextResponse> {
  let index = -1

  async function next(): Promise<NextResponse> {
    index++
    if (index >= middlewares.length) {
      return res
    }
    const func = middlewares[index]
    return func(req, res, next)
  }

  return next()
}
