import type { StorybookConfig } from '@storybook/nextjs'

const config: StorybookConfig = {
  env: {
    TEST_URL: 'https://www.google.com',
  },
  stories: [
    '../stories/**/*.mdx',
    '../stories/**/*.stories.@(js|jsx|mjs|ts|tsx)',
    '../components/**/*.stories.@(js|jsx|mjs|ts|tsx)',
  ],
  addons: [
    '@storybook/addon-onboarding',
    '@storybook/addon-essentials',
    '@chromatic-com/storybook',
    '@storybook/addon-interactions',
  ],
  framework: {
    name: '@storybook/nextjs',
    options: {},
  },
  staticDirs: ['../public'],
  webpackFinal: async (config) => {
    const fileLoaderRule = config.module?.rules?.find((rule) => {
      if (!rule || typeof rule !== 'object') {
        return
      }

      const test = rule.test

      if (!test) {
        return
      }

      if (typeof test === 'string') {
        return test === '.svg'
      }

      if (typeof test === 'function') {
        return test('.svg')
      }

      if (Array.isArray(test)) {
        return test.includes('.svg')
      }

      if (test instanceof RegExp) {
        return test.test('.svg')
      }

      return false
    })

    if (fileLoaderRule && typeof fileLoaderRule === 'object') {
      fileLoaderRule.exclude = /\.svg$/
    }

    config.module?.rules?.push({
      test: /\.svg$/,
      use: ['@svgr/webpack'],
    })

    return config
  },
}
export default config
