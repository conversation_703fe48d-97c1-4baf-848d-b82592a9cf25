'use client'

import { Table, Popover, Typography } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import { useGameRecords } from '../hooks/useGameRecords'
import { useState } from 'react'
import type { GameRecord } from '@libs/apis/src/typings/game'
import { dayjs } from '@libs/utils'
import { useTranslate } from '@tolgee/react'
import CodeDict from '@/components/client/code-dicts'
import { CodeDetail } from '@libs/typings'
import { getIcon } from '@/utils/getIcon'
const { Paragraph } = Typography

/**
 * 游戏记录 - 结算状态
 */
export enum SettlementEnum {
  /** 进行中 */
  Ongoing = 'ing',
  /** 未结算 */
  NotSettled = '0',
  /** 结算中 */
  Billing = '4',
  /** 赢 */
  win = '1',
  /** 异常冻结 */
  ExceptionallyFrozen = 'frozen',
  /** 输 */
  lose = '2',
  /** 未知状态 */
  UnknownStatus = '5',
  /** 平 */
  Draw = '3',
}

const TableList = ({ type }: { type: 'settled' | 'unsettled' }) => {
  const { t } = useTranslate()
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [queryParams, setQueryParams] = useState({
    pageNum: currentPage,
    pageSize: pageSize,
    startTime: dayjs().startOf('day').valueOf(),
    endTime: dayjs().endOf('day').valueOf(),
  })

  const { records, total, isLoading } = useGameRecords(queryParams, type)

  // 使用 useMemo 缓存 columns 定义，避免不必要的重渲染
  const columns: ColumnsType<GameRecord> = [
    {
      title: t('order.order_create_time', {}),
      dataIndex: 'createdByTime',
      key: 'createdByTime',
      render: (time) => new Date(time).toLocaleString(),
    },
    {
      title: t('order.game_play', {}),
      dataIndex: 'gamePlay',
      key: 'gamePlay',
      align: 'center',
    },
    {
      title: t('order.order_no', {}),
      dataIndex: 'orderNo',
      key: 'orderNo',
      align: 'center',
      render: (text) => (
        <Popover
          content={<div className="text-neutral-1 text-sm">{text}</div>}
          placement="top"
          trigger="hover"
          overlayClassName="betting-record-popover"
        >
          <span className="flex items-center gap-1 text-sm">
            {text.length > 20 ? `${text.slice(0, 10)}...${text.slice(-10)}` : text}
            <Paragraph
              className="m-0 h-4 w-4"
              copyable={{
                icon: [getIcon('copy', { className: 'w-4 h-4 text-gs-900' })],
                tooltips: '',
                text: text || '',
              }}
            ></Paragraph>
          </span>
        </Popover>
      ),
    },
    {
      title: t('order.game_name', {}),
      dataIndex: 'gameName',
      key: 'gameName',
      align: 'center',
    },
    {
      title: t('order.order_amount', {}),
      dataIndex: 'amount',
      key: 'amount',
      align: 'center',
      render: (amount, record) => `${amount} ${record.coin || 'USDT'}`,
    },
    {
      title: t('order.real_amount', {}),
      dataIndex: 'realAmount',
      key: 'realAmount',
      align: 'center',
      render: (amount, record) => `${amount} ${record.coin || 'USDT'}`,
    },
    {
      title: t('order.profit_loss', {}),
      dataIndex: 'profitLoss',
      key: 'profitLoss',
      align: 'center',
      render: (amount, record) => `${amount} ${record.coin || 'USDT'}`,
    },
    {
      title: t('order.settlement_time', {}),
      dataIndex: 'settDtt',
      key: 'settDtt',
      align: 'center',
      render: (time) => (time ? new Date(time).toLocaleString() : '-'),
    },
    {
      title: t('order.settlement_status', {}),
      dataIndex: 'clearingStatus',
      key: 'clearingStatus',
      align: 'right',
      render: (status: keyof typeof SettlementEnum) => {
        const statusMap: Record<SettlementEnum, { text: string; color: string }> = {
          [SettlementEnum.Ongoing]: {
            text: 'In progress',
            color: 'var(--color-warning-2)', // 紫色
          },
          [SettlementEnum.NotSettled]: {
            text: 'Not settled',
            color: 'var(--color-warning-2)', // 灰色
          },
          [SettlementEnum.Billing]: {
            text: 'Billing',
            color: 'var(--color-warning-2)', // 橙色
          },
          [SettlementEnum.win]: {
            text: 'win',
            color: 'var(--color-success-3)', // 绿色
          },
          [SettlementEnum.lose]: {
            text: 'lose',
            color: 'var(--color-error-3)', // 粉色
          },
          [SettlementEnum.ExceptionallyFrozen]: {
            text: 'Frozen',
            color: 'var(--color-bgs-900)', // 灰色
          },
          [SettlementEnum.UnknownStatus]: {
            text: 'unknown',
            color: 'var(--color-bgs-900)', // 灰色
          },
          [SettlementEnum.Draw]: {
            text: 'Draw',
            color: '', // 灰色
          },
        } as const

        const statusConfig = statusMap[status as SettlementEnum] || statusMap[SettlementEnum.UnknownStatus]

        return (
          <span className="text-sm font-bold" style={{ color: statusConfig.color }}>
            <CodeDict type={CodeDetail.RaCenterSettState} textId={status} />
          </span>
        )
      },
    },
  ]

  // 处理分页变化
  const handlePaginationChange = (page: number, size: number) => {
    setCurrentPage(page)
    setPageSize(size)
    setQueryParams({
      ...queryParams,
      pageNum: page,
      pageSize: size,
    })
  }

  return (
    <div className="overflow-auto">
      <Table
        columns={columns}
        dataSource={records}
        loading={isLoading}
        pagination={
          total >= 10
            ? {
                current: currentPage,
                pageSize: pageSize,
                total: total,
                showSizeChanger: true,
                showQuickJumper: true,
                onChange: handlePaginationChange,
                position: ['bottomCenter'],
              }
            : false
        }
        rowKey="id"
        scroll={{ x: 'max-content', y: 500 }}
        sticky
      />
    </div>
  )
}

export default TableList
