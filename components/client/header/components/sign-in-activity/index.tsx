import { FC, useCallback, useEffect, useRef, useState } from 'react'
import { App, Button, Modal } from 'antd'
import { createApi, SignInMissionListItem } from '@libs/apis/src'
import { dayjs } from '@libs/utils/src/dayjs'
import { GivenTypeEnum } from './sign-in'
import { useTranslate } from '@tolgee/react'
import Calendar from './components/calendar'
import CalendarTick from '@/public/svg/common/calendar-tick.svg'
import SignInSuccessModal from './components/sign-in-success-modal'
import SignInRuleModal from './components/sign-in-rule-modal'
import { ArrowRight2 } from 'iconsax-react'
import useSWRMutation from 'swr/mutation'

interface SignInModalProps {
  open: boolean
  onClose: () => void
}

const getDefaultAward = () => {
  return { quantity: '0', symbol: '', ruleType: 'single', givenType: 'random', days: '1' }
}

const initSignInData = {
  todayAwards: [getDefaultAward()],
  continuityDays: 1,
  againDays: 1,
  periodDay: 1,
  signedList: [] as string[],
  rules: '',
}

const startDate = dayjs().startOf('month').startOf('day').valueOf()
const endDate = dayjs().add(1, 'month').endOf('month').startOf('day').valueOf()

const SignInModal: FC<SignInModalProps> = ({ open, onClose }) => {
  const { t } = useTranslate()
  const { message } = App.useApp()
  const [signInMissions, setSignInMissions] = useState<SignInMissionListItem[]>([])
  const [signInData, setSignInData] = useState<typeof initSignInData>(initSignInData)
  const [signInSuccessModal, setSignInSuccessModal] = useState(false)
  const [signInRuleModal, setSignInRuleModal] = useState(false)
  const [signInPeriod, setSignInPeriod] = useState('')
  const render = useRef(false)
  const giveRender = (mission: SignInMissionListItem) => {
    if (mission.givenType === GivenTypeEnum.fixed) {
      return `${t('activity.got')} ${mission.baseQuantity} ${mission.symbol}`
    } else {
      return `${t('activity.got_randomly')} ${mission.baseQuantity} ~ ${mission.maxQuantity} ${mission.symbol}`
    }
  }

  const { trigger: getSignInfo } = useSWRMutation('signInfo', async () => {
    const res = await createApi().apis.activity.getSignInfo()
    return res.data
  })

  const { trigger: setSubmitSign } = useSWRMutation(
    'apis.activity.setSubmitSign',
    async (_, params: { arg: string }) => {
      const res = await createApi().apis.activity.setSubmitSign({
        data: { signId: params.arg },
      })
      return res.data
    },
  )

  const { trigger: getSignPeriod } = useSWRMutation(
    'apis.activity.getSignPeriod',
    async (_, params: { arg: string }) => {
      const res = await createApi().apis.activity.getSignPeriod({
        data: {
          signId: params.arg,
          startDate: startDate,
          endDate: endDate,
        },
      })
      return res.data
    },
  )

  const { trigger: getSignTaskList } = useSWRMutation(
    'apis.activity.getSignTaskList',
    async (_, params: { arg: string }) => {
      const res = await createApi().apis.activity.getSignTaskList({
        data: { signId: params.arg },
      })
      return res.data
    },
  )

  const fetchActivity = useCallback(async () => {
    const signInInfo = await getSignInfo()
    if (!Number(signInInfo?.id)) {
      message.error(t('promo.not_started', {}))
      return
    }

    setSignInPeriod(signInInfo!.id)

    try {
      const signInResponse = await setSubmitSign(signInInfo!.id)
      if (signInResponse) {
        setSignInSuccessModal(true)
      }
    } catch (error) {
      console.log(error)
    }

    // 取得個人簽到資訊
    const signInPeriodList = await getSignPeriod(signInInfo!.id)
    const dateArray: string[] = []
    for (let i = 0; i < signInPeriodList.signedList.length; i++) {
      const currentTime = signInPeriodList.signedList[i]
      dateArray[i] = dayjs(Number(currentTime)).format('YYYY-MM-DD')
    }
    setSignInData({ ...signInPeriodList, rules: signInInfo.remark, signedList: dateArray })

    // 取得簽到相關活動
    const signTaskList = await getSignTaskList(signInInfo!.id)
    setSignInMissions(signTaskList)
  }, [])

  useEffect(() => {
    if (render.current) {
      return
    }
    render.current = true
    fetchActivity()
  }, [])

  return (
    <Modal title={t('activity.sign_in')} footer={false} open={open} onCancel={onClose}>
      <div className="flex h-full flex-col items-center overflow-y-auto overflow-x-hidden">
        {/* 簽到訊息 */}
        {signInData && (
          <>
            <div className="mb-4 mt-5 flex w-full flex-col items-center justify-start">
              <div className="flex w-full items-center justify-between">
                <div className="flex items-center">
                  <h2 className="pr-4 text-base font-semibold text-shades-100">
                    {t('activity.get_daily_sign_in_rewards')}
                  </h2>
                  {signInData.todayAwards[0] && (
                    <span className="text-lg text-success-3">{`+ ${signInData.todayAwards[0].quantity} ${signInData.todayAwards[0].symbol}`}</span>
                  )}
                </div>
                <div className="flex cursor-pointer items-center" onClick={() => setSignInRuleModal(true)}>
                  <span className="text-xs text-gs-900">{t('activity.rules')}</span>
                  <ArrowRight2 size="20" color="rgba(147, 164, 184, 1)" />
                </div>
              </div>
              <div className="flex w-full items-center justify-between text-shades-100">
                <div className="flex items-center py-3">
                  <span className="font-semibold">{t('activity.signed_in_continuously')}</span>
                  <span className="font-semibolds px-1 text-primary-3">{signInData.continuityDays}</span>
                  <span className="font-semibold">{t('activity.days')}</span>
                </div>
                {/* <div className="flex items-center gap-2">
                  <div className="text-xs text-gs-900">{t('activity.sign_in_reminder')}</div>
                  <Switch onClick={handleSwitch} checked={!isRemind} />
                </div> */}
              </div>
              <div className="w-full text-xs text-shades-100">
                <span>{t('activity.sign_more')}</span>
                <span className="px-1">{signInData.againDays}</span>
                <span>{t('activity.can_get_rewards')}</span>
              </div>
            </div>
          </>
        )}
        <Calendar signInDates={signInData.signedList} />
        {/* 签到奖励任務 */}
        {signInMissions.length > 0 &&
          signInMissions.map((mission, index) => (
            <div className="flex w-full flex-col py-4" key={`mission-${index}`}>
              <div className="mb-2 text-base font-semibold text-gs-900">{t('activity.continuous_sign_rewards')}</div>
              <div className="bg-linear-2 flex-col rounded-2xl border border-primary-1 px-3 py-4 shadow">
                <div className="flex items-center justify-start">
                  <CalendarTick className="size-6 text-primary-3" />
                  <div className="ml-2 flex flex-col gap-2 text-gs-900">
                    <div className="font-semibold">{`${t('activity.signed_in_continuously')} ${mission.days} ${t('days')}`}</div>
                    <div className="text-xs font-semibold">{giveRender(mission)}</div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        <Button className="mt-4 h-11" type="primary" block onClick={onClose}>
          {t('activity.got_it')}
        </Button>
      </div>
      {signInSuccessModal && (
        <SignInSuccessModal
          award={signInData.todayAwards[0]}
          open={signInSuccessModal}
          onClose={() => setSignInSuccessModal(false)}
        />
      )}
      {signInRuleModal && (
        <SignInRuleModal
          content={signInData.rules}
          signInPeriod={signInPeriod}
          open={signInRuleModal}
          onClose={() => setSignInRuleModal(false)}
        />
      )}
    </Modal>
  )
}

export default SignInModal
