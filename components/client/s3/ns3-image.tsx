'use client'

import Image from 'next/image'

import { S3_IMAGE_URL } from '@libs/env'
import { cn } from '@libs/utils'
import { useState, useMemo } from 'react'

type Props = {
  path: string
  errorPath?: string
  className?: string
}

export default function Ns3Image({ path, errorPath, className }: Props) {
  const [error, setError] = useState(false)

  const imgUrl = useMemo(() => {
    if (error && errorPath) {
      return `${S3_IMAGE_URL}/zz/${errorPath}`
    }

    return `${S3_IMAGE_URL}/zz/${path}`
  }, [path, error, errorPath])

  return (
    <Image
      src={imgUrl}
      alt="ns3-image"
      layout="fill"
      objectFit="cover"
      className={cn('overflow-hidden', className)}
      onError={() => {
        setError(true)
      }}
    />
  )
}
