{"name": "zz-web", "version": "0.1.359", "private": true, "scripts": {"use-env:dev": "cp .env.dev .env.local", "use-env:uat": "cp .env.uat .env.local", "use-env:prod": "cp .env.prod .env.local", "windows:use-env:dev": "copy .env.dev .env.local", "windows:use-env:uat": "copy .env.uat .env.local", "dev": "pnpm use-env:dev && next dev  --turbopack", "dev:uat": "pnpm use-env:uat && next dev  --turbopack", "dev:prod": "pnpm use-env:prod && next dev  --turbopack", "windows:dev": "pnpm windows:use-env:dev && start /B pnpm json-server & next dev", "windows:dev:uat": "pnpm windows:use-env:uat && start /B next dev", "build": "next build", "start": "next start", "lint": "next lint && eslint --no-cache .", "prettier": "npx prettier --write '**/*.ts' '**/*.tsx'", "storybook": "pnpm storybook:update:files && storybook dev -p 6006", "storybook:update:files": "node ./stories/bingo/list-svg-files.mjs", "build-storybook": "storybook build", "vitest": "vitest --run", "tolgee": "pnpm run tolgee:pull && pnpm run google-sheet:pull", "tolgee:pull": "cross-env TOLGEE_API_KEY=tgpak_gm3f6ztem4zgo33kgn2ximlunjyxgndun4zg2mlkhbxg2zq tolgee pull && node ./i18n/gen-ns.js", "google-sheet:pull": "node ./scripts/pull-error-code-message/index.mjs", "google-sheet:pull:web": "cross-env GOOGLE_PROJECT=web node ./scripts/pull-error-code-message/index.mjs", "prepare": "husky", "docker:build": "docker build -f Dockerfile -t web-2z-dev .", "bump": "pnpm version patch --no-git-tag-version", "tag:prod": "git tag v$npm_package_version && git push origin v$npm_package_version", "tag:uat": "git tag v$npm_package_version-uat && git push origin v$npm_package_version-uat", "commit:release": "git commit -am \"chore: release v$npm_package_version\" && git push origin dev", "release:uat": "pnpm bump && pnpm commit:release && pnpm tag:uat", "release:prod": "pnpm tag:prod"}, "dependencies": {"@ant-design/nextjs-registry": "^1.0.2", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@libs/api": "workspace:^0.1.0", "@libs/env": "workspace:^0.1.0", "@libs/utils": "workspace:^0.1.0", "@lucky-canvas/react": "^0.1.13", "@modules/auth": "workspace:^0.1.0", "@modules/event-tracking": "workspace:^0.1.0", "@svgr/webpack": "^8.1.0", "@tanstack/react-query": "^5.74.11", "@tolgee/react": "^6.0.1", "accept-language": "^3.0.20", "ahooks": "^3.8.4", "antd": "^5.24.2", "antd-img-crop": "^4.24.0", "bignumber.js": "^9.3.0", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "echarts-for-react": "^3.0.2", "firebase": "^11.5.0", "framer-motion": "^10.16.0", "he": "^1.2.0", "html2canvas-pro": "^1.5.11", "iconsax-react": "^0.0.8", "jose": "^5.9.6", "jsencrypt": "^3.3.2", "keen-slider": "^6.8.6", "lodash-es": "^4.17.21", "lottie-react": "^2.4.1", "motion": "^12.5.0", "next": "15.2.3", "node-rsa": "^1.1.1", "qrcode.react": "^4.2.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-fast-marquee": "^1.6.5", "react-hook-form": "^7.54.2", "react-use": "^17.6.0", "swr": "^2.3.1", "tailwind-merge": "^3.0.1", "wagmi": "^2.15.1", "zod": "^3.24.1", "zustand": "^5.0.3"}, "devDependencies": {"@chromatic-com/storybook": "3.2.4", "@commitlint/cli": "^19.7.1", "@commitlint/config-conventional": "^19.7.1", "@eslint/eslintrc": "^3", "@libs/typings": "workspace:^0.1.0", "@storybook/addon-essentials": "8.5.3", "@storybook/addon-interactions": "8.5.3", "@storybook/addon-onboarding": "8.5.3", "@storybook/blocks": "8.5.3", "@storybook/nextjs": "8.5.3", "@storybook/react": "8.5.3", "@storybook/test": "8.5.3", "@tolgee/cli": "^2.9.0", "@tolgee/format-icu": "^6.1.0", "@tolgee/web": "^6.1.0", "@types/crypto-js": "^4.2.2", "@types/he": "^1.2.3", "@types/lodash-es": "^4.17.12", "@types/next-pwa": "^5.6.9", "@types/node": "^20", "@types/node-rsa": "^1.1.4", "@types/react": "^19", "@types/react-dom": "^19", "@vitest/coverage-v8": "^3.0.8", "cross-env": "^7.0.3", "daisyui": "^4.12.22", "eslint": "^8", "eslint-config-next": "15.1.6", "eslint-config-prettier": "^10.0.2", "eslint-plugin-storybook": "^0.11.2", "google-auth-library": "^9.15.1", "google-spreadsheet": "^4.1.4", "husky": "^9.1.7", "jsdom": "^26.0.0", "lint-staged": "^15.4.3", "next-pwa": "^5.6.0", "pino-pretty": "^13.0.0", "postcss": "^8", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "storybook": "8.5.3", "tailwindcss": "^3.4.1", "typescript": "5.7.3", "vitest": "^3.0.5"}, "volta": {"node": "20.18.3", "pnpm": "9.15.6"}}