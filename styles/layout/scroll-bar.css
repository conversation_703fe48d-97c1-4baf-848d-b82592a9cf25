/* scrollbar 配置 */
html {
  --scroll-bar-width: 10px;
  --scroll-bar-height: 4px;
  --scroll-bar-radius: 8px;
  /* firefox */
  scrollbar-width: auto;

  /* Chrome, Safari */
  ::-webkit-scrollbar {
    width: var(--scroll-bar-width);
    height: var(--scroll-bar-height);
  }

  ::-webkit-scrollbar-track {
    background: var(--nc-11);
    border-radius: var(--scroll-bar-radius);
  }

  ::-webkit-scrollbar-thumb {
    background: var(--nc-04);
    border-radius: var(--scroll-bar-radius);
  }

  ::-webkit-scrollbar-corner {
    background: var(--nc-11);
  }

  /* 隐藏 scrollbar */
  .no-scrollbar {
    /* Firefox */
    scrollbar-width: none;

    /* IE and Edge */
    -ms-overflow-style: none;

    ::-webkit-scrollbar {
      display: none;
    }

    ::-moz-scrollbar {
      display: none;
    }
  }
}

html .silderbar-box {
  scrollbar-width: none;
}
