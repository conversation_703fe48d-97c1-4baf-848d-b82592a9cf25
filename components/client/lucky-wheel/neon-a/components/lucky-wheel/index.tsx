import { cn } from '@libs/utils/src'
import { motion } from 'motion/react'
import Image, { StaticImageData } from 'next/image'
import { useMemo, useState } from 'react'

// components
import { BaseCard } from '../cards'
import Wheel from './wheel'

// images
import RuleIcon from '@/components/client/lucky-wheel/images/rules.svg'
import btnRotateCn from '@/components/client/lucky-wheel/images/neon-a/btn_rotate_cn.png'
import btnRotateEn from '@/components/client/lucky-wheel/images/neon-a/btn_rotate_en.png'

import recordPlate from '@/components/client/lucky-wheel/images/neon-a/record_plate.png'
import icon1 from '@/components/client/lucky-wheel/images/neon-a/icon_1.png'
import icon2 from '@/components/client/lucky-wheel/images/neon-a/icon_2.png'
import icon3 from '@/components/client/lucky-wheel/images/neon-a/icon_3.png'
import icon4 from '@/components/client/lucky-wheel/images/neon-a/icon_4.png'
import icon5 from '@/components/client/lucky-wheel/images/neon-a/icon_5.png'
import icon6 from '@/components/client/lucky-wheel/images/neon-a/icon_6.png'
import icon7 from '@/components/client/lucky-wheel/images/neon-a/icon_7.png'
import icon8 from '@/components/client/lucky-wheel/images/neon-a/icon_8.png'
import icon9 from '@/components/client/lucky-wheel/images/neon-a/icon_9.png'
import icon10 from '@/components/client/lucky-wheel/images/neon-a/icon_10.png'

// utils
import { padNumberString } from '@/components/client/lucky-wheel/utils'
import { sortBy } from 'lodash-es'
import { LottieS3 } from '../../../lottie-s3'
import { useStarWheel } from '../../../hooks'

export interface LuckyWheelProps {
  currencyUnit?: string
  width?: number
  lang: 'en' | 'zh'
  wheelCount?: number
  prizeList: Array<{
    icon?: StaticImageData
    label: string
    isHighlight?: boolean
  }>
  className?: string
  onEnd?: (res: string) => void
  onShowRules?: (show: boolean) => void
}

export default function LuckyWheelNeonA(
  props: LuckyWheelProps & {
    onClose?: () => void
    handleStart?: () => Promise<number | undefined>
  },
) {
  const { width = 345, lang = 'en' } = props
  const [isSpinning, setIsSpinning] = useState(false)
  const { wheelRef, handleStart } = useStarWheel({
    isSpinning,
    setIsSpinning,
    handleStart: props?.handleStart,
  })

  const icons = [icon1, icon2, icon3, icon4, icon5, icon6, icon7, icon8, icon9, icon10]

  const prizeList = useMemo(() => {
    return props.prizeList.map((item, index) => {
      return {
        ...item,
        icon: icons[index],
      }
    })
  }, [icons, props.prizeList])

  return (
    <div className="flex items-center">
      <BaseCard width={width} className={cn('pt-[25%]')} onClose={props?.onClose}>
        {/* rules */}
        <RuleIcon
          className={cn(
            'absolute bottom-[20%] right-[10%] z-[10] h-[34px] w-[34px] cursor-pointer rounded-xl border p-1',
          )}
          onClick={() => props?.onShowRules?.(true)}
        />

        <div
          className={cn('relative h-[120px]', {
            'px-[5%]': lang === 'zh',
          })}
        >
          <div className="absolute left-0 top-[100%] m-auto h-[200px] w-full">
            <LottieS3
              lottieSource={
                lang === 'en'
                  ? 'https://tw-s3-image.s3.ap-southeast-1.amazonaws.com/lottie/luckwheel-1-logo_en.json'
                  : 'https://tw-s3-image.s3.ap-southeast-1.amazonaws.com/lottie/luckwheel-1-logo_cn.json'
              }
              className={cn('scale-[3]')}
            />
          </div>
        </div>

        <div className={cn('m-auto -mt-[7%] flex h-[9%] w-[80%] items-center gap-2')}>
          {sortBy(prizeList, (item) => -item.label)
            .slice(0, 3)
            .map((item, index) => (
              <div
                key={index}
                className="flex flex-1 flex-col items-center rounded-lg border border-white bg-[#30189c] pt-1"
              >
                <Image src={item.icon} width={30} height={30} alt="地主" />
                <motion.div
                  initial={{ opacity: 0, y: -30 }}
                  animate={{
                    opacity: isSpinning ? 0 : 1,
                    y: isSpinning ? 30 : 0,
                    height: isSpinning ? 0 : 'auto',
                    marginTop: isSpinning ? 0 : '-5%',
                  }}
                  exit={{ opacity: 0, y: 30 }}
                  transition={{ duration: 0.3 }}
                  className={cn('pt-1 text-center text-3xs text-[12px] font-semibold leading-4 text-shades-100')}
                >
                  {padNumberString(item.label)} {props?.currencyUnit}
                </motion.div>
              </div>
            ))}
        </div>

        <motion.div
          animate={{
            scale: isSpinning ? 1.1 : 1,
            transition: {
              duration: 0.3,
            },
          }}
        >
          <Wheel
            ref={wheelRef}
            className="m-auto"
            prizeList={prizeList}
            lang={props.lang}
            width={width * 0.75}
            onEnd={(res) => {
              setIsSpinning(false)
              props?.onEnd?.(res)
            }}
            handleStart={handleStart}
          />
        </motion.div>

        <motion.div
          animate={{
            opacity: isSpinning ? 0 : 1,
            y: isSpinning ? 30 : 0,
            height: isSpinning ? 0 : 'auto',
          }}
        >
          <div className="text-center text-sm">
            {
              //剩余Spin次数:{props?.wheelCount} 次
              lang === 'zh'
                ? `剩余Spin次数: ${props?.wheelCount} 次`
                : `Remaining spin times: ${props?.wheelCount} times`
            }
          </div>

          <Image
            src={lang === 'en' ? btnRotateEn : btnRotateCn}
            width={200}
            height={50}
            className="z-2 relative m-auto h-[auto] w-[50%]"
            alt="action"
            onClick={handleStart}
          ></Image>

          <div
            className="m-auto flex gap-2 px-[24px] pt-[12px]"
            style={{
              backgroundImage: `url('${recordPlate.src}')`,
              backgroundSize: 'cover',
              backgroundPosition: 'top center',
              height: '64px',
            }}
          >
            {sortBy(prizeList, (item) => Number(item.label))
              .slice(0, 3)
              .map((item, index) => (
                <div key={index} className="flex flex-1 items-center rounded-lg">
                  <Image src={icons[index]} width={18} height={18} alt="地主" />
                  <div className={cn('pl-2 text-center text-3xs text-[12px] font-semibold leading-4 text-shades-100')}>
                    {padNumberString(item.label)}
                  </div>
                </div>
              ))}
          </div>
        </motion.div>
      </BaseCard>
    </div>
  )
}
