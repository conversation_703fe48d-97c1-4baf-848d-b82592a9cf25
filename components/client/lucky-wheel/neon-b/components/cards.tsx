import { useRef, useState } from 'react'

import { cn } from '@libs/utils'
import { motion } from 'motion/react'
import Image from 'next/image'

// images
import ImageBackground from '@/components/client/lucky-wheel/images/neon-b/background.png'
import CloseIcon from '@/components/client/lucky-wheel/images/neon-b/button-close.png'
import MusicOffIcon from '@/components/client/lucky-wheel/images/neon-b/button-music-off.png'
import MusicIcon from '@/components/client/lucky-wheel/images/neon-b/button-music.png'

// audios
import { backgroundMusic1, closeCancelAudio } from '@/components/client/lucky-wheel/audios'

// utils
import { formatNumber } from '@/libs/apis'

// components
import { BtnShare, BtnStart } from './buttons'
import { TextLoseImage, TextWinImage } from './texts'
import { TitleLoseImage, TitleWinImage } from './title'

// types
import { BackdropProps, BaseCardProps, CardProps } from '@/components/client/lucky-wheel/types'

// constants
const DEFAULT_WIDTH = 350
const DEFAULT_LOCALE = 'en'

export function Backdrop({ className, children, ...props }: BackdropProps) {
  return (
    <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
      <div {...props} style={{ backgroundColor: 'rgba(0, 0, 0, 0.7)' }} className={cn('absolute inset-0', className)}>
        {children}
      </div>
    </motion.div>
  )
}

export function BaseCard({ width, buttonsDivClassName, children, className, onClose, ...props }: BaseCardProps) {
  const musicRef = useRef(backgroundMusic1)
  const [isMusicOn, setIsMusicOn] = useState(!musicRef.current.paused)

  const handleMusicOn = () => {
    setIsMusicOn(true)
    musicRef.current.play()
  }

  const handleMusicOff = () => {
    setIsMusicOn(false)
    musicRef.current.pause()
  }

  const handleClose = () => {
    handleMusicOff()
    closeCancelAudio.play()
    onClose?.()
  }

  return (
    <motion.div initial={{ opacity: 0, scale: 0 }} animate={{ opacity: 1, scale: 1 }} exit={{ opacity: 0, scale: 0 }}>
      <div {...props} role="neon-b-base-card" style={{ width: `${width}px` }} className={cn('relative', className)}>
        {/* background */}
        <Image src={ImageBackground} alt="bg-image" />

        {/* content */}
        <div className="absolute inset-0">{children}</div>

        {/* buttons */}
        <div className={cn('absolute inset-x-0 -top-14 flex items-center justify-between px-3', buttonsDivClassName)}>
          {isMusicOn ? (
            <Image src={MusicIcon} alt="music-icon" className="w-4" onClick={handleMusicOff} />
          ) : (
            <Image src={MusicOffIcon} alt="music-off-icon" className="w-4" onClick={handleMusicOn} />
          )}
          <Image src={CloseIcon} alt="close-icon" className="w-3" onClick={handleClose} />
        </div>
      </div>
    </motion.div>
  )
}

export function WinCard({
  locale = DEFAULT_LOCALE,
  reward = 0,
  width = DEFAULT_WIDTH,
  onClose,
  onStart,
  onShare,
}: CardProps) {
  return (
    <BaseCard data-title="neon-b-win-card" width={width} onClose={onClose}>
      <TitleWinImage locale={locale} className="absolute -top-[110px] w-full" />
      <div className="absolute inset-x-0 bottom-[50px]">
        <p
          style={{ fontFamily: '-apple-system', fontWeight: 'bold' }}
          className="text-center text-7xl leading-none text-[#B4EDF3]"
        >
          {formatNumber(reward)}
        </p>
        <TextWinImage locale={locale} className="mx-auto mt-4 w-[275px]" />
        <div className={cn('mt-[70px] grid justify-center gap-2', 'invisible')}>
          <BtnStart locale={locale} className="w-[120px]" onClick={onStart} />
          <BtnShare locale={locale} className="w-[120px]" onClick={onShare} />
        </div>
      </div>
    </BaseCard>
  )
}

export function LoseCard({ locale = DEFAULT_LOCALE, width = DEFAULT_WIDTH, onClose, onStart, onShare }: CardProps) {
  return (
    <BaseCard data-title="neon-b-lose-card" width={width} buttonsDivClassName="-top-2" onClose={onClose}>
      <TitleLoseImage locale={locale} className="absolute -top-[130px] w-full" />
      <div className="absolute inset-x-0 bottom-[50px]">
        <TextLoseImage locale={locale} className="mx-auto w-[300px]" />
        <div className={cn('mt-[60px] grid justify-center gap-2', 'invisible')}>
          <BtnStart locale={locale} className="w-[120px]" onClick={onStart} />
          <BtnShare locale={locale} className="w-[120px]" onClick={onShare} />
        </div>
      </div>
    </BaseCard>
  )
}
