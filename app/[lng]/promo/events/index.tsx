import { createApi, OfferData } from '@libs/apis'
import useSWR from 'swr'
import { useTranslate } from '@tolgee/react'
import Icon from '@/components/client/icon'
import { useState } from 'react'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import { onJump } from '@/constants/routes'
import EmptyContent from '@/components/client/empty-content'
import { Spin } from 'antd'
import { useSignInModalStore } from '@/store/sign-in-modal'
import { useActivityStore } from '@/store/activity'
import LuckyWheelModel from '../(activity)/lucky-wheel/components'

const useActivityTypeList = () => {
  const { data: typeList } = useSWR('getActivityTypeList', async () => {
    const { data } = await createApi().apis.activity.getActivityTypeList()
    return data || []
  })

  return typeList || []
}

const useActivityInfoList = (code: string | number) => {
  const { data: infoList, isLoading } = useSWR(['getActivityInfoList', code], async () => {
    const { data } = await createApi().apis.activity.getActivityInfoList({ data: { code } })
    return data || []
  })

  return { infoList: infoList || [], isLoading }
}

type TypeItemProps = {
  isActive: boolean
  onClick: () => void
  code: string
  name: string
}

const TypeItem = ({ isActive, onClick, code, name }: TypeItemProps) => {
  return (
    <div
      className={`mt-[10px] flex h-[48px] cursor-pointer items-center gap-2 overflow-hidden rounded-[6px] bg-gs-400 px-4 ${isActive ? 'text-primary-1' : 'text-bgs-900'}`}
      onClick={onClick}
    >
      <Icon name={code} size={18} />
      {name}
    </div>
  )
}

export default function Events() {
  const router = useRouter()
  const { showLuckWheelId, setShowLuckWheel } = useActivityStore()

  const [code, setCode] = useState<string | number>('all')
  const typeList = useActivityTypeList()
  const { infoList, isLoading } = useActivityInfoList(code)
  const { t } = useTranslate()

  const handleClick = async (data: OfferData) => {
    const { activiteType, id, url } = data

    if (url) {
      window.open(url)
      return
    }

    switch (activiteType) {
      case 'bet':
        return onJump({ jumpType: 'REBATE_ACTIVITY', jumpContent: String(id), subType: '' })
      case 'subsidy':
        return onJump({ jumpType: 'EMERGENCY', jumpContent: String(id), subType: '' })
      case 'rank':
        return onJump({ jumpType: 'RANK', jumpContent: String(id), subType: '' })
      case 'redenvelope':
        return onJump({ jumpType: 'REDENVELOPE', jumpContent: String(id), subType: '' })
      case 'fission':
        const { data } = await createApi().apis.activity.getUserSignIn({
          data: {
            activityId: id.toString(),
          },
        })
        if (data) {
          return onJump({ jumpType: 'FISSION', jumpContent: String(id), subType: '' })
        }
        return
      case 'phone':
        return onJump({ jumpType: 'PHONE', jumpContent: String(id), subType: '' })
      case 'character':
        return onJump({ jumpType: 'CHARACTER_COLLECTION', jumpContent: String(id), subType: '' })
      case 'sign':
        useSignInModalStore.getState().openModal()
        return
      case 'agent':
        return onJump({ jumpType: 'AGENT_SUPPORT_PROGRAM', jumpContent: String(id), subType: '' })
      case 'depositBonus':
        return onJump({ jumpType: 'DEPOSIT_BONUS', jumpContent: String(id), subType: '' })
      case 'coupon':
        return onJump({ jumpType: 'COUPON', jumpContent: String(id), subType: '' })
      case 'luckyRoulette':
        setShowLuckWheel(true, id)
        return
      default:
        return router.push(`/promo/${id}`)
    }
  }

  return (
    <div className="flex gap-9">
      <div className="w-[120px] md:w-[168px]">
        <TypeItem
          isActive={code === 'all'}
          code={'allGame'}
          name={t('homePage.allGame')}
          onClick={() => setCode('all')}
        />
        {typeList.map((el) => (
          <TypeItem
            key={el.code}
            isActive={code === el.id}
            code={el.code}
            name={el.name}
            onClick={() => setCode(el.id)}
          />
        ))}
      </div>

      {!infoList?.length && !isLoading && (
        <div className="h-full w-full flex-1 items-center justify-center">
          <EmptyContent noDataText={t('common.no_new_mission', {})} />
        </div>
      )}

      <Spin className="min-h-[50vh] w-full flex-1" spinning={isLoading}>
        <div className="grid flex-1 cursor-pointer grid-cols-1 content-start gap-4 md:grid-cols-2">
          {infoList.map((el, i) => (
            <Image
              key={i}
              className="rounded-[16px]"
              src={el.bannerImgUrl}
              width={486}
              height={225}
              alt={el.bannerImgUrl}
              unoptimized
              onClick={() => handleClick(el)}
            />
          ))}
        </div>
        {showLuckWheelId && <LuckyWheelModel />}
      </Spin>
    </div>
  )
}
