import { S3_IMAGE_URL } from '@libs/env/src'

const publicPath = ['/img', '/svg', '/scripts', '/api']

export function isPublicPath(path: string) {
  return publicPath.some((p) => path.startsWith(p))
}

export const getS3TWImageUrl = (src: string) => {
  if (!/xsaas/.test(src)) src = `/xsaas${src.startsWith('/') ? src : `/${src}`}`
  const url = new URL(src, 'https://tw-s3-image.s3.ap-southeast-1.amazonaws.com')
  return url.toString()
}

export const getS3PaymentImage = (src: string) => {
  return `${S3_IMAGE_URL}/com/pay/${src}`
}
