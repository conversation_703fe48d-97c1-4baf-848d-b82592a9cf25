/* body.theme-2z.dark */
body.dark {
  /* primary */
  --color-primary-1: #ffc039;
  --color-primary-2: #ffcd91;
  --color-primary-3: #e98a18;
  --color-primary-4: rgba(251, 107, 18, 0.7);

  /* secondary */
  --color-secondary-1: rgba(255, 192, 57, 0.1);
  --color-secondary-2: rgba(255, 192, 57, 0.15);
  --color-secondary-3: rgba(255, 192, 57, 0.1);

  /* gray scale(gs) */
  --color-gs-100: #101416;
  --color-gs-200: #151a1d;
  --color-gs-300: #1c2226;
  --color-gs-400: #252a2b;
  --color-gs-500: #262e33;
  --color-gs-600: #363f48;
  --color-gs-700: #5d5f64;
  --color-gs-800: #979797;
  --color-gs-900: #d9d9d9;

  /* background scale(bgs) */
  --color-bgs-100: #243037;
  --color-bgs-200: #263240;
  --color-bgs-300: #2b3541;
  --color-bgs-400: #32404f;
  --color-bgs-500: #37424a;
  --color-bgs-600: #414d5c;
  --color-bgs-700: #54657b;
  --color-bgs-800: #b1bad3;
  --color-bgs-900: #8a9ba7;

  /* shades */
  --color-shades-0: #151a1d;
  --color-shades-100: #ffffff;

  /* success */
  --color-success-1: #006e26;
  --color-success-2: #00872e;
  --color-success-3: #30e377;
  --color-success-4: #30e377;
  --color-success-5: #42dc9e;
  --color-success-6: #72c17e;
  --color-success-7: #a1d2a8;
  --color-success-8: rgba(48, 227, 119, 0.1);
  --color-success-9: rgba(66, 220, 158, 0.2);
  --color-success-10: #00ae3c26;

  /* error */
  --color-error-1: #692231;
  --color-error-2: #b63a54;
  --color-error-3: #fc2d42;
  --color-error-4: #fc2d42;
  --color-error-5: #fc2d42;
  --color-error-6: #f3abb6;
  --color-error-7: #feebef;
  --color-error-8: rgba(252, 45, 66, 0.1);
  --color-error-9: rgba(255, 87, 104, 0.2);

  /* warning */
  --color-warning-1: #f26411;
  --color-warning-2: #f99438;
  --color-warning-3: #ffc039;
  --color-warning-4: #e9a805;
  --color-warning-5: rgba(241, 174, 61, 0.1);
  --color-warning-6: #e9a8051a;
  --color-warning-7: rgba(233, 168, 5, 0.1);
  --color-warning-8: rgba(255, 192, 57, 0.2);

  /* info */
  --color-info-1: #1f5feb;
  --color-info-2: #1877f2;
  --color-info-3: #73a0fb66;

  /* basic */
  --color-black: #000000;
  --color-white: #ffffff;

  --color--on-button-primary: var(--color-black);

  /* Background and UI Colors */

  .color-linear-1 {
    background: linear-gradient(49.93deg, var(--color-primary-2) 0%, #ff910e 100%);
  }

  .color-linear-2 {
    background: linear-gradient(270deg, #ebffd0 0%, var(--color-white) 100%);
  }

  .color-gradient-1 {
    background: linear-gradient(180deg, #50b16c 0%, #a3e0ae 100%);
  }

  .color-gradient-2 {
    background: linear-gradient(180deg, var(--color-error-4) 0%, var(--color-error-6) 100%);
  }

  --gray-1: 202 21% 18%;
  --gray-2: 204 15% 25%;
  --gray-2: 205 14% 60%;
  .bg-gray-1 {
    background-color: color-mix(in srgb, hsl(var(--gray-1)) 50%, transparent);
  }

  --color-gray-1: hsl(var(--gray-1));
  --color-gray-2: hsl(var(--gray-2));
  --color-gray-3: hsl(var(--gray-3));
}
