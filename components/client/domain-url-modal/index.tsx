'use client'

import { useState, useEffect, useRef } from 'react'
import { Modal, Button, Typography, App } from 'antd'
import { useTranslate } from '@tolgee/react'
import { getIcon } from '@/utils/getIcon'
import { useLayoutStore } from '@/store/layout'
import Image from 'next/image'
// eslint-disable-next-line import/no-unresolved
import html2canvas from 'html2canvas-pro'
const { Paragraph } = Typography
import { useRouter } from 'next/navigation'
import { useLocalStorageState } from 'ahooks'
import { dayjs } from '@libs/utils/src'

import useSWR from 'swr'
import { createApi } from '@libs/apis'
import { isMerchant920017 } from '@libs/env/src'

const DomainUrlModal = () => {
  const [visible, setVisible] = useState(false)
  const { t } = useTranslate()
  const { layoutData } = useLayoutStore()
  const { logo, darkLogo } = layoutData?.dbJson?.config?.brand.image || {}
  const logoImg = darkLogo || logo
  const router = useRouter()
  const { message } = App.useApp()

  // 域名列表
  const { data: domainData } = useSWR('getDomainPopupList', async () => {
    const { data } = await createApi().apis.system.getDomainPopupList()
    return data || []
  })

  // 确保域名是字符串数组
  const domainList = Array.isArray(domainData) ? domainData : []

  // 使用 localStorage 存储显示状态
  const [domainModalStorage, setDomainModalStorage] = useLocalStorageState<{
    lastShownTime: number
  }>('domain-url-modal-storage', {
    listenStorageChange: true,
  })

  // 检查是否今天已经显示过弹窗
  const hasShownToday = () => {
    if (!domainModalStorage?.lastShownTime) return false
    return dayjs(domainModalStorage.lastShownTime).isSame(dayjs(), 'day')
  }

  // 使用useEffect控制弹窗显示
  useEffect(() => {
    if (domainList.length > 0 && !hasShownToday()) {
      setVisible(true)
      // 更新显示时间
      setDomainModalStorage({
        lastShownTime: dayjs().valueOf(),
      })
    }
  }, [domainList, domainModalStorage, setDomainModalStorage])

  // 添加对弹窗内容的引用
  const contentRef = useRef<HTMLDivElement>(null)

  const handleClose = () => {
    setVisible(false)
  }

  const handleSaveImage = async () => {
    if (!contentRef.current) return

    try {
      message.loading('正在生成图片...')

      // 使用html2canvas-pro直接截图
      const canvas = await html2canvas(contentRef.current, {
        backgroundColor: '#20272F',
        scale: 2,
        useCORS: true,
        allowTaint: true,
      })

      // 将canvas转为图片URL
      const imgData = canvas.toDataURL('image/png')

      // 创建下载链接
      const link = document.createElement('a')
      link.download = 'domain-info.png'
      link.href = imgData
      link.click()

      message.success('图片已保存')
    } catch (error) {
      console.error('保存图片失败:', error)
      message.error('保存图片失败，请重试！')
    }
  }

  // 如果没有域名数据，不显示弹窗
  if (domainList.length === 0) {
    return null
  }

  // 获取主域名
  const mainDomain = domainList.length > 0 ? domainList[0] : window.location.hostname

  // 是否显示第二部分（只有多个域名时显示第二部分）
  const showSecondPart = domainList.length > 1

  return (
    <Modal
      title={<div className="text-base text-shades-100">{t('domain.reminder', '如果網址無效改怎麼辦？')}</div>}
      open={visible}
      footer={null}
      maskClosable={false}
      onCancel={handleClose}
      width={490}
    >
      <div className="rounded-lg bg-white py-4 text-sm font-medium text-shades-100">
        <div ref={contentRef}>
          <div className="mb-6 px-2">
            <div className="mb-2">{t('domain.find_through_desktop', '1.通过桌面图标上的URL找到我们')}</div>
            <div className="flex h-[68px] items-center justify-between rounded-lg bg-gs-200 px-4">
              <div className="flex items-center">
                {logoImg && (
                  <Image src={logoImg} alt="logo" width={120} height={40} className="cursor-pointer object-contain" />
                )}
                <span className="ml-2 max-w-[200px] overflow-hidden text-ellipsis whitespace-nowrap">{mainDomain}</span>
              </div>
              <Paragraph
                className="m-0"
                copyable={{
                  icon: [getIcon('copy', { className: 'w-6 h-6 text-gs-900' })],
                  tooltips: '',
                  text: mainDomain,
                }}
              ></Paragraph>
            </div>
          </div>

          {/* 第二部分：记住以下域名 - 只有在多个域名时才显示 */}
          {showSecondPart && (
            <div className="mb-6 px-2">
              <div className="mb-4">{t('domain.remember_domains', '2.或记住以下域名')}</div>
              {domainList.slice(1).map((domain: string, index: number) => (
                <div key={index} className="mb-3 flex h-[52px] items-center justify-between rounded-lg bg-gs-200 px-4">
                  <span className="max-w-[330px] overflow-hidden text-ellipsis whitespace-nowrap text-sm">
                    {domain}
                  </span>
                  <Paragraph
                    className="m-0"
                    copyable={{
                      icon: [getIcon('copy', { className: 'w-6 h-6 text-gs-900' })],
                      tooltips: '',
                      text: domain,
                    }}
                  ></Paragraph>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 底部按钮 */}
        <div className="flex gap-4 px-2">
          {!isMerchant920017() && (
            <Button
              className="flex-1 rounded-lg bg-gs-500 text-base font-normal"
              size="large"
              onClick={() => router.push('/app-download')}
            >
              {t('domain.download_app', '下载APP')}
            </Button>
          )}
          <Button
            className="flex-1 rounded-lg bg-primary-1 text-base font-normal"
            type="primary"
            size="large"
            onClick={handleSaveImage}
          >
            {t('domain.save_image', '保存图片')}
          </Button>
        </div>
      </div>
    </Modal>
  )
}

export default DomainUrlModal
