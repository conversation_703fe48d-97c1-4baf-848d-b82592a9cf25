.notify-drawer {
  &.ant-drawer {
    .ant-drawer-header {
      @apply bg-gs-200;
      box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
      border-bottom: none;
    }
  }
  .ant-segmented {
    &.ant-segmented-block {
      .ant-segmented-item {
        /* flex: 1 1 auto; */
        /* min-width: auto; */
      }
    }

    .ant-segmented-item-label {
      white-space: nowrap;
      overflow: visible;
      text-overflow: inherit;
      /* min-width: 72px; */
    }
    .ant-segmented-group {
      /* display: grid;
      grid-template-columns: repeat(4, 1fr); */
    }
  }
}
