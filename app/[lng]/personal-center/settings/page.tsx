'use client'
import ContentHeaderBar from '@/components/client/content-header-bar'
import { useTranslate } from '@tolgee/react'
import { Button, Col, ConfigProvider, List, Row } from 'antd'
import CurrencyExchange from 'public/svg/essetional/icon_currency_exchange.svg'
import EditAddress from 'public/svg/essetional/Edit_address.svg'
import PayMethod from 'public/svg/essetional/pay_method.svg'
import SelfExclusionIcon from 'public/svg/essetional/self_exclusion.svg'
import { useMemo, useState } from 'react'
import { useRouter } from 'next/navigation'
import WalletSettingsModal from '@/components/client/wallet/WalletSettingModal'
import { useAuthStore } from '@modules/auth/src/store'
import useCoin from '@/components/client/header/hooks/useCoin'
import RequestSelfExclusionModal from '@/components/client/self-exclusion-modal/RequestSelfExclusionModal'
import StartSelfExclusionModal from '@/components/client/self-exclusion-modal/StartSelfExclusionModal'
import { useSecondaryVerificationModalStore } from '@/components/client/secodary-verification/store'
import SecondaryVerificationModal from '@/components/client/secodary-verification'
import useSelfLimit from './hooks/useSelfLimit'
import { SelfExclusionMode } from './constants'
import UnlockSelfExclusionModal from '@/components/client/self-exclusion-modal/UnlockSelfExclusionModal'
import { useUserStore } from '@/store/user'
import { VerifyCodeType } from '@libs/typings/src'

const PersonalCenterSettingsPage = () => {
  const { t } = useTranslate()
  const { userSetting, userInfo } = useAuthStore()
  const { fiatCurrency } = useCoin()
  const { handleSelfLimit } = useSelfLimit()

  const [settingOpen, setSettingOpen] = useState(false)
  const [selftExclusionMode, setSelftExclusionMode] = useState('')
  const [unlockSelfExclusionMode, setUnlockSelfExclusionMode] = useState(false)

  const { setOpen: openSecondaryVerificationModal, open: secondaryVerificationModalOpen } =
    useSecondaryVerificationModalStore()
  const { setBusinessType } = useUserStore()

  const handleSelfLimitSuccess = async () => {
    const status = userInfo?.selfLimitStatus === 1 ? 0 : 1
    handleSelfLimit(status)
  }

  const router = useRouter()
  const securityVerification = useMemo(
    () =>
      [
        {
          key: 'converted_currency',
          title: t('personal_center.converted_currency', {}),
          description: t('personal_center.Please_converted_currency', {}),
          icon: <CurrencyExchange className="h-[22px] w-[22px] text-bgs-900"></CurrencyExchange>,
          text: userSetting?.comLegalCurrency?.currencyTypeCd,
          actionRender: (
            <Button
              color="primary"
              variant="link"
              className="w-[80px] p-0 text-sm leading-[18px]"
              onClick={() => {
                setSettingOpen(true)
              }}
            >
              {t('personal_center.setup', {})}
            </Button>
          ),
        },
        {
          key: 'withdrawal_management',
          title: t('personal_center.withdrawal_management', {}),

          description: t('personal_center.manage_withdrawal_management', {}),
          icon: <EditAddress className="h-[22px] w-[22px] text-bgs-900"></EditAddress>,
          actionRender: (
            <Button
              color="primary"
              variant="link"
              className="w-[80px] p-0 text-sm leading-[18px]"
              onClick={() => {
                router.push('/management/withdrawal-address')
              }}
            >
              {t('personal_center.manage', {})}
            </Button>
          ),
        },
        {
          key: 'payMethod',
          title: t('personal_center.payout_method', {}),
          description: t('personal_center.select_payout_method', {}),
          icon: <PayMethod className="h-[22px] w-[22px] text-bgs-900"></PayMethod>,
          actionRender: (
            <Button
              color="primary"
              variant="link"
              className="w-[80px] p-0 text-sm leading-[18px]"
              onClick={() => {
                router.push('/management/payout-method')
              }}
            >
              {t('personal_center.setup', {})}
            </Button>
          ),
        },
        userInfo?.globalSelfLimitStatus === 1 && {
          key: 'self_exclusion',
          title: t('personal_center.self_exclusion', {}),
          icon: <SelfExclusionIcon className="h-[22px] w-[22px] text-bgs-900"></SelfExclusionIcon>,
          actionRender:
            userInfo?.selfLimitStatus === 0 ? (
              <Button
                color="primary"
                variant="link"
                className="w-[80px] p-0 text-sm leading-[18px]"
                onClick={() => {
                  setSelftExclusionMode('request')
                }}
              >
                {t('personal_center.setup', {})}
              </Button>
            ) : (
              <div className="text-sm leading-[18px] text-warning-3" onClick={() => setUnlockSelfExclusionMode(true)}>
                {t('personal_center.in_self_limitation', {})}
              </div>
            ),
        },
      ].filter(Boolean),
    [fiatCurrency, userInfo],
  )

  return (
    <ConfigProvider
      theme={{
        token: {
          colorSplit: 'var(--color-bgs-600)',
        },
        components: {
          Divider: {
            colorSplit: 'var(--color-bgs-300)',
          },
          Alert: {
            colorText: 'var(--color-primary-1)',
          },
          Button: {
            controlHeightLG: 48,
          },
          Input: {
            controlHeightLG: 51,
            controlHeight: 40,
            activeBorderColor: 'var(--color-primary-1)',
            activeBg: 'var(--color-gs-200)',
            fontSize: 12,
          },
          Select: {
            controlHeight: 40,
          },
          Form: {
            fontSize: 14,
          },

          InputNumber: {
            controlHeight: 40,
            fontSize: 12,
          },
          Segmented: {
            itemColor: 'var(--color-bgs-900)',
          },
          Skeleton: {
            gradientFromColor: 'var(--color-shades-0)',
            controlHeightXS: 8,
            paragraphLiHeight: 12,
            blockRadius: 0,
          },
        },
      }}
    >
      <div className="m-auto max-w-[1200px]">
        <ContentHeaderBar title={t('header.preferenceSettings', {})}></ContentHeaderBar>
        <div className="mt-6">
          <div>
            <div className="mb-6 text-xl font-semibold text-shades-100">
              {t('personal_center.feature_settings', {})}
            </div>
            <List
              className="mb-12 bg-gs-200"
              size="large"
              itemLayout="horizontal"
              bordered
              dataSource={securityVerification}
              renderItem={(item) => (
                <List.Item>
                  <Row key="1" className="w-full">
                    <Col span={16}>
                      <div className="flex items-center gap-4">
                        {item.icon}
                        <div>
                          <div className="text-base font-medium leading-5 text-shades-100">{item.title}</div>
                          <div className="mt-2 w-[508px] text-sm leading-[18px] text-bgs-900">{item.description}</div>
                        </div>
                      </div>
                      <div></div>
                    </Col>

                    <Col span={4} className="flex items-center gap-2">
                      <span className="text-sm text-shades-100">{item?.text}</span>
                    </Col>

                    <Col span={4} className="flex items-center justify-end">
                      {item.actionRender}
                    </Col>
                  </Row>
                </List.Item>
              )}
            />
          </div>
          <WalletSettingsModal open={settingOpen} onClose={() => setSettingOpen(false)} />
          <RequestSelfExclusionModal
            open={selftExclusionMode === SelfExclusionMode.request}
            onClose={() => setSelftExclusionMode('')}
            onSuccess={() => {
              setSelftExclusionMode(SelfExclusionMode.start)
            }}
          />
          <StartSelfExclusionModal
            open={selftExclusionMode === SelfExclusionMode.start}
            onClose={() => setSelftExclusionMode('')}
            onSuccess={() => {
              setSelftExclusionMode(SelfExclusionMode.confirmStart)
              setBusinessType(VerifyCodeType.WithdrawAddress)
              openSecondaryVerificationModal(true)
            }}
          />
          <UnlockSelfExclusionModal
            open={unlockSelfExclusionMode}
            onClose={() => setUnlockSelfExclusionMode(false)}
            onSuccess={() => {
              setUnlockSelfExclusionMode(false)
              setBusinessType(VerifyCodeType.WithdrawAddress)
              openSecondaryVerificationModal(true)
            }}
          />
          {secondaryVerificationModalOpen && <SecondaryVerificationModal onSuccess={handleSelfLimitSuccess} />}
        </div>
      </div>
    </ConfigProvider>
  )
}
export default PersonalCenterSettingsPage
