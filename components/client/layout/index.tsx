'use client'

// React imports
import { useEffect, useMemo } from 'react'
import { usePathname } from 'next/navigation'

// Ant Design imports
import { ConfigProvider, Layout as AntdLayout, theme as AntdTheme, App, Affix } from 'antd'

import '@ant-design/v5-patch-for-react-19'

import { useLayout } from './hook'

// Utility and API imports
import { Props } from './hook'
import { Grid } from 'antd'
// Misc imports
import SwrConfig from '../swr'
import { ApiProvider } from '../api'
import { useLayoutStore } from '@/store/layout'
import LeftMenuBar from './left-menu-bar'
import H5BottomMenu from './h5-bottom-menu'
import { useSilderBar } from './left-menu-bar/hooks'
import Header from './header'
import Footer from './footer'
import AuthForm from './auth-form'

const { Content } = AntdLayout
const { useBreakpoint } = Grid
export default function Layout(props: Props) {
  const { locale, showFooter, theme, rest, t, isLogin } = useLayout(props)
  const screens = useBreakpoint()
  const { showLeftMenu } = useSilderBar()
  const { setLayoutData, initVipAndAgent, setH5Url, getHiddenModule } = useLayoutStore()
  const { footerJson, headerJson, dbJson, userAgreement, cookiePolicy, children } = props
  const pathname = usePathname()
  // 检查是否为特殊页面（如403页面）
  const isSpecialPage =
    pathname?.endsWith('/403') || pathname?.endsWith('/third-party-page') || pathname?.endsWith('/503')

  // const isShowCookiePolicy = useMemo(() => {
  //   return dbJson?.config?.cookiePolicy === 'open'
  // }, [dbJson])

  useEffect(() => {
    isLogin && initVipAndAgent()
  }, [isLogin])

  useEffect(() => {
    setLayoutData({ footerJson, headerJson, dbJson, userAgreement, cookiePolicy })
    setH5Url(props.h5Url)
  }, [])

  const layoutMl = useMemo(() => {
    return screens.md ? (!showLeftMenu ? '240px' : '74px') : '0'
  }, [screens.md, showLeftMenu])

  // 如果是特殊页面，只渲染子内容，不包含布局
  if (isSpecialPage) {
    return (
      <SwrConfig>
        <ConfigProvider
          theme={{
            ...theme,
            algorithm: AntdTheme.darkAlgorithm,
          }}
          locale={locale}
          {...rest}
        >
          <App>
            <ApiProvider>{children}</ApiProvider>
          </App>
        </ConfigProvider>
      </SwrConfig>
    )
  }

  return (
    <SwrConfig>
      <ConfigProvider
        theme={{
          ...theme,
          algorithm: AntdTheme.darkAlgorithm,
        }}
        locale={locale}
        {...rest}
        form={{
          validateMessages: {
            required: t('common_required', { name: '${label}' }),
          },
        }}
        modal={{
          centered: true,
        }}
      >
        <App>
          <ApiProvider>
            <AntdLayout role="main-layout" className="flex">
              <LeftMenuBar />
              <AntdLayout>
                <Affix
                  offsetTop={0}
                  className="z-10"
                  style={{
                    marginLeft: layoutMl,
                  }}
                >
                  <Header isLogin={false} />
                </Affix>

                <Content
                  id="app-content"
                  className="flex flex-1 flex-col"
                  style={{
                    marginLeft: layoutMl,
                  }}
                >
                  <div
                    className={`relative flex w-[100vw] flex-1 flex-col items-center overflow-x-hidden px-[16px] md:w-full`}
                  >
                    <div className="flex-[1 1 100%] layout-container min-h-screen w-full overflow-visible">
                      {children}
                    </div>
                  </div>
                  <Footer
                    showFooter={showFooter}
                    hiddeModule={{ ...getHiddenModule() }}
                    dbJson={dbJson}
                    footerJson={footerJson}
                    userAgreement={userAgreement}
                  />
                  <AuthForm />
                </Content>
              </AntdLayout>
              <H5BottomMenu />
              <AuthForm />
            </AntdLayout>
          </ApiProvider>
        </App>
      </ConfigProvider>
    </SwrConfig>
  )
}
