import { useTranslate } from '@tolgee/react'
import Image from 'next/image'

type NoDataImageType = {
  name?: string
  size?: [number, number]
  footerText?: string
  className?: string
  hasTheme?: boolean
  show?: boolean
  whetherManyBusiness?: boolean
  hideImg?: boolean
}

function NoDataImage({ show = false, size = [103, 93], className, footerText, hideImg = false }: NoDataImageType) {
  const { t } = useTranslate()
  return (
    show && (
      <div className={`flex flex-col items-center justify-center ${className ? className : 'gap-2'} `}>
        <Image
          className={`${hideImg ? 'hidden' : ''}`}
          src="/svg/missing_page/icon_noorder.svg"
          alt=""
          width={size[0]}
          height={size[1]}
        />

        <div className="text-sm text-bgs-900">{footerText || t('common.empty', {})}</div>
      </div>
    )
  )
}
export default NoDataImage
