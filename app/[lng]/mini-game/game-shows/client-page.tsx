'use client'

import ContentHeaderBar from '@/components/client/content-header-bar'
import { GameShowHeaderIcon } from './constants'
import GlobalSearch from '../../home/<USER>/home/<USER>'

import { useGameShow, useGameShowList } from './hooks/useGameShow'
import { useProviders } from '../details/[gameId]/hooks/useProviders'
import { useTranslate } from '@tolgee/react'
import GameList from '../../home/<USER>/home/<USER>'
import { useSilderBarStore } from '@/store/silder-bar'
import { GameListItem } from '@libs/apis/src'
import SearchLoad from '@/components/client/load-more'
import GameShowsList from '@/components/client/game-show-list/game-show-list'
import Icon from '@/components/client/icon'

type Props = {
  code: keyof typeof GameShowHeaderIcon
  typeCd: string
  lng: string
  supplierId: string
  tabId: string
  groupId: string
  lanuages: { label: string; value: string }[]
  title: string
}

export default function GameShowsClientPage({ code, typeCd, supplierId, lng, title, groupId, tabId }: Props) {
  const { t } = useTranslate()

  const { gameData, isGameListLoading, pageNum, setPageNum } = useGameShow({
    typeCd,
    lng,
    supplierId,
    groupId,
    tabId,
  })
  const { providerList } = useProviders()
  const { silder } = useSilderBarStore()
  const findMenus = silder.menus?.find((item) => item?.title?.name === 'silderBar.gameTypeRecommend')
  const findTitle =
    findMenus?.menus?.find((item) => String(item.id) === typeCd || String(item.id) === tabId)?.name || title
  const { gameList } = useGameShowList((gameData?.list as GameListItem[]) || [])

  return (
    <div className="m-auto flex max-w-[1200px] flex-col">
      <ContentHeaderBar
        title={findTitle}
        Icon={() => <Icon name={code} className="h-[80%] w-[100px] object-contain text-bgs-900" />}
      />
      <div className="flex flex-col gap-8 py-8">
        <GlobalSearch />
        <GameShowsList gameList={gameList || []} />
        <SearchLoad
          total={gameData?.total || 0}
          pageSize={gameList.length || 0}
          pageNum={pageNum}
          isGameListLoading={isGameListLoading}
          onLoad={setPageNum}
        />
        <div className="mt-10">
          <GameList
            code={'Baccarat'}
            title={t('mini_game.game_provider', {})}
            dataList={providerList}
            type="providers"
          />
        </div>
      </div>
    </div>
  )
}
