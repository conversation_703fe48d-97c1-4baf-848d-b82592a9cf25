import type { ConfigProviderProps } from 'antd'
import Close from '@/public/svg/essetional/close.svg'

export const antdConfig: ConfigProviderProps = {
  message: { className: 'global-message' },
  modal: { closeIcon: <Close className="h-5 w-5 text-gs-900"></Close> },
  theme: {
    cssVar: true,
    hashed: false,
    token: {
      colorPrimary: 'var(--color-primary-1)',
      colorInfo: 'var(--color-primary-1)',
      fontSize: 16,
      fontFamily: 'Saira,Saira Fallback ',
    },
    components: {
      Alert: {
        lineWidth: 0,
        colorInfo: 'var(--color-primary-1)',
        colorInfoBg: 'var(--color-secondary-3)',
        defaultPadding: 12,
        fontSize: 14,
      },
      Layout: {
        siderBg: 'var(--color-gs-200)',
        footerBg: 'var(--color-gs-200)',
        bodyBg: 'var(--color-shades-0)',
      },
      Card: {
        headerPadding: 8,
        headerPaddingSM: 8,
        headerFontSize: 14,
        bodyPadding: 8,
        bodyPaddingSM: 8,
      },
      Button: {
        borderRadius: 8,
        fontWeight: 400,
        colorPrimary: 'var(--color-on-button-primary)',
        defaultBg: 'rgb(41,52,61)',
        defaultActiveColor: 'rgb(41,52,61)',
        defaultActiveBorderColor: 'rgb(41,52,61)',
        defaultActiveBg: 'rgb(41,52,61)',
        defaultBorderColor: 'rgb(41,52,61)',
        defaultColor: 'rgb(255,255,255)',
        defaultHoverBg: 'rgb(41,52,61)',
        defaultHoverColor: 'rgb(255,255,255)',
        defaultHoverBorderColor: 'rgb(41,52,61)',
        colorTextDisabled: 'rgb(151, 151, 151)',
        colorBgContainerDisabled: 'rgb(93, 95, 100)',
      },
      Badge: {
        colorError: 'rgb(235,75,109)',
        indicatorHeightSM: 12,
        textFontSizeSM: 10,
        colorBorderBg: 'rgba(255,255,255,0)',
        dotSize: 8,
      },
      List: {
        padding: 32,
        itemPaddingLG: '28px 42px',
        descriptionFontSize: 14,
        avatarMarginRight: 16,
        marginXXS: 8,
        colorBorder: 'var(--color-bgs-600)',
        colorSplit: 'var(--color-bgs-600)',
      },
      Tooltip: {
        colorBgSpotlight: 'rgb(32,39,47)',
        fontSize: 11,
        paddingXS: 8,
        controlHeight: 20,
        paddingSM: 8,
      },
      Tabs: {
        colorBorderSecondary: 'rgba(255,255,255, 0)',
        inkBarColor: 'var(--color-primary-1)',
        itemActiveColor: 'var(--color-primary-1)',
        itemSelectedColor: 'var(--color-primary-1)',
        itemHoverColor: 'var(--color-primary-1)',
      },
      Input: {
        // 修改large尺寸的控件高度
        controlHeightLG: 45,
        colorTextPlaceholder: 'var(--color-bgs-800)',
        colorText: 'var(--color-shades-100)',
        activeBg: 'var(--color-gs-200)',
        activeBorderColor: 'var(--color-gs-200)',
        colorBorder: 'var(--color-bgs-400)',
        activeShadow: 'none',
        hoverBg: 'var(--color-gs-600)',
        hoverBorderColor: 'var(--color-gs-600)',
      },
      InputNumber: {
        colorTextPlaceholder: 'var(--color-bgs-800)',
        colorText: 'var(--color-shades-100)',
        activeBg: 'var(--color-gs-200)',
        activeBorderColor: 'var(--color-gs-200)',
        colorBorder: 'var(--color-bgs-400)',
        activeShadow: 'none',
        hoverBg: 'var(--color-gs-600)',
        hoverBorderColor: 'var(--color-gs-600)',
      },
      Select: {
        colorTextPlaceholder: 'var(--color-bgs-800)',
        colorText: 'var(--color-shades-100)',
        activeBorderColor: 'var(--color-gs-200)',
        colorBorder: 'var(--color-bgs-400)',
        hoverBorderColor: 'var(--color-gs-600)',
        selectorBg: 'var(--color-gs-200)',
      },
      Slider: {
        /* 这里是你的组件 token */
        dotSize: 12,
        dotBorderColor: 'var(--color-primary-1)',
        dotActiveBorderColor: 'var(--color-primary-1)',
        railBg: 'var(--color-warning-2)',
        railHoverBg: 'var(--color-warning-2)',
        handleSize: 18,
        handleSizeHover: 18,
        handleActiveOutlineColor: 'transparent',
        handleActiveColor: 'var(--color-primary-1)',
        trackHoverBg: 'var(--color-primary-1)',
        trackBg: 'var(--color-primary-1)',
      },
      Segmented: {
        fontSizeLG: 14,
        fontSize: 14,
        controlHeight: 40,
        controlHeightLG: 46,
        trackPadding: 4,
        itemColor: 'var(--color-shades-100)',
        itemHoverBg: 'var(--color-gs-600)',
        itemHoverColor: 'var(--color-shades-100)',
        itemSelectedBg: '#202a31',
        itemSelectedColor: 'var(--color-on-button-primary)',
        trackBg: 'var(--color-gs-200)',
      },
      Modal: {
        contentBg: 'var(--color-shades-0)',
        headerBg: 'var(--color-shades-0)',
        footerBg: 'var(--color-shades-0)',
        titleColor: 'var(--color-shades-100)',
        colorIcon: 'var(--color-shades-100)',
        colorIconHover: 'var(--color-bgs-900)',
      },
      Message: {
        fontSize: 14,
        fontSizeLG: 14,
        lineHeight: 1.3,
      },
      DatePicker: {
        activeBg: 'var(--color-gs-200)',
        activeBorderColor: 'var(--color-gs-200)',
        cellHeight: 44,
        cellHoverBg: 'var(--color-primary-1)',
        cellHoverWithRangeBg: 'var(--color-secondary-1)',
        cellActiveWithRangeBg: 'var(--color-secondary-1)',
        cellRangeBorderColor: 'var(--color-primary-1)',
        cellWidth: 44,
        colorBgElevated: 'var(--color-bgs-200)',
        colorText: 'var(--color-shades-100)',
        colorIcon: 'var(--color-shades-100)',
        colorTextDisabled: 'var(--color-gs-600)',
      },
      Table: {
        bodySortBg: 'transparent',
        headerBg: 'transparent',
        borderColor: 'transparent',
        headerColor: 'var(--color-bgs-900)',
        headerBorderRadius: 0,
        headerSplitColor: 'transparent',
      },
      Pagination: {
        itemActiveBg: 'var(--color-primary-1)',
        itemBg: 'transparent',
        itemInputBg: '#263240',
      },
      Radio: {
        buttonBg: 'transparent',
      },
    },
  },
  button: {
    autoInsertSpace: false,
  },
}
