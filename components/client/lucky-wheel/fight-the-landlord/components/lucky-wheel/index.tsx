import { cn } from '@libs/utils/src'
import { motion } from 'motion/react'
import Image, { StaticImageData } from 'next/image'
import { useMemo, useState } from 'react'

// components
import { BaseCard } from '../cards'
import Wheel from './wheel'

import { sortBy } from 'lodash-es'

// images
import RuleIcon from '@/components/client/lucky-wheel/images/rules.svg'
import btnRotateCn from '@/components/client/lucky-wheel/images/landlord/btn_rotate_cn.png'
import btnRotateEn from '@/components/client/lucky-wheel/images/landlord/btn_rotate_en.png'

import icon1 from '@/components/client/lucky-wheel/images/landlord/icon_1.png'
import icon2 from '@/components/client/lucky-wheel/images/landlord/icon_2.png'
import icon3 from '@/components/client/lucky-wheel/images/landlord/icon_3.png'
import icon4 from '@/components/client/lucky-wheel/images/landlord/icon_4.png'
import icon5 from '@/components/client/lucky-wheel/images/landlord/icon_5.png'
import icon6 from '@/components/client/lucky-wheel/images/landlord/icon_6.png'
import icon7 from '@/components/client/lucky-wheel/images/landlord/icon_7.png'
import icon8 from '@/components/client/lucky-wheel/images/landlord/icon_8.png'
import icon9 from '@/components/client/lucky-wheel/images/landlord/icon_9.png'
import icon10 from '@/components/client/lucky-wheel/images/landlord/icon_10.png'

// utils
import { padNumberString } from '@/components/client/lucky-wheel/utils'
import { LottieS3 } from '../../../lottie-s3'
import { useStarWheel } from '../../../hooks'

export interface LuckyWheelProps {
  currencyUnit?: string
  width?: number
  wheelCount?: number
  lang: 'en' | 'zh'
  prizeList: Array<{
    icon?: StaticImageData
    label: string
    isHighlight?: boolean
  }>
  className?: string
  onEnd?: (res: string) => void
  onShowRules?: (show: boolean) => void
}

export default function LuckyWheelLandlord(
  props: LuckyWheelProps & {
    onClose?: () => void
    handleStart?: () => Promise<number | undefined>
  },
) {
  const { width = 323, lang = 'en' } = props
  const [isSpinning, setIsSpinning] = useState(false)
  const icons = [icon1, icon2, icon3, icon4, icon5, icon6, icon7, icon8, icon9, icon10]

  const { wheelRef, handleStart } = useStarWheel({
    isSpinning,
    setIsSpinning,
    handleStart: props?.handleStart,
  })

  const prizeList = useMemo(() => {
    return props.prizeList.map((item, index) => {
      return {
        ...item,
        icon: icons[index],
      }
    })
  }, [icons, props.prizeList])

  return (
    <div className="flex items-center">
      <BaseCard
        width={width}
        className={cn('pt-[120px]')}
        buttonsDivClassName={cn('top-[22%]')}
        onClose={props?.onClose}
      >
        <div
          className={cn('h-[160px] px-[5%]', {
            '-mt-[40px] h-[200px]': lang === 'en',
          })}
        >
          <LottieS3
            lottieSource={
              lang === 'en'
                ? 'https://tw-s3-image.s3.ap-southeast-1.amazonaws.com/lottie/luckwheel-0-logo_en.json'
                : 'https://tw-s3-image.s3.ap-southeast-1.amazonaws.com/lottie/luckwheel-0-logo_cn.json'
            }
            className={cn('m-auto', {
              'scale-[1.48]': lang === 'zh',
              'scale-[1.3]': lang === 'en',
            })}
          />
        </div>

        {/* rules */}
        <RuleIcon
          className={cn('absolute left-0 top-[40px] z-[10] h-[34px] w-[34px] rounded-xl border p-1')}
          onClick={() => {
            props?.onShowRules?.(true)
          }}
        />

        <div className="m-auto -mt-[5%] flex h-[9%] w-[80%] items-center justify-evenly gap-4">
          {sortBy(prizeList, (item) => -item.label)
            .slice(0, 3)
            .map((item, index) => (
              <div
                key={index}
                className="flex flex-col items-center rounded-lg border border-white bg-[#30189c] px-2 text-center"
              >
                <Image src={item.icon} width={30} height={30} alt="地主" />
                <motion.div
                  initial={{ opacity: 0, y: -30 }}
                  animate={{
                    opacity: isSpinning ? 0 : 1,
                    y: isSpinning ? 30 : 0,
                    height: isSpinning ? 0 : 'auto',
                    marginTop: isSpinning ? 0 : '-5%',
                  }}
                  exit={{ opacity: 0, y: 30 }}
                  transition={{ duration: 0.3 }}
                  className={cn('text-center text-3xs text-[12px] font-semibold leading-4 text-shades-100')}
                >
                  {padNumberString(item.label)} {props?.currencyUnit}
                </motion.div>
              </div>
            ))}
        </div>

        <motion.div
          animate={{
            scale: isSpinning ? 1.1 : 1,
            transition: {
              duration: 0.3,
            },
          }}
        >
          <Wheel
            ref={wheelRef}
            className="m-auto"
            prizeList={prizeList}
            lang={props.lang}
            width={width * 0.88}
            onEnd={(res) => {
              setIsSpinning(false)
              props?.onEnd?.(res)
            }}
            handleStart={handleStart}
          />
        </motion.div>

        <motion.div
          animate={{
            opacity: isSpinning ? 0 : 1,
            y: isSpinning ? 30 : 0,
            height: isSpinning ? 0 : 'auto',
            marginTop: isSpinning ? 0 : '-5%',
          }}
        >
          <Image
            src={lang === 'en' ? btnRotateEn : btnRotateCn}
            width={200}
            height={50}
            className="z-2 relative m-auto h-[auto] w-[50%]"
            alt="action"
            onClick={handleStart}
          />

          <div className="-mt-2 text-center text-sm">
            {
              //剩余Spin次数:{props?.wheelCount} 次
              lang === 'zh'
                ? `剩余Spin次数: ${props?.wheelCount} 次`
                : `Remaining spin times: ${props?.wheelCount} times`
            }
          </div>

          <div className="m-auto flex h-[9%] w-[80%] items-center gap-2">
            {sortBy(prizeList, (item) => Number(item.label))
              ?.slice(0, 3)
              .map((item, index) => (
                <div key={index} className="flex flex-1 items-center gap-2 rounded-lg">
                  <Image src={icons[index]} width={18} height={18} alt="地主" />
                  <div className={cn('text-center text-3xs text-[12px] font-semibold leading-4 text-shades-100')}>
                    {padNumberString(item.label)}
                  </div>
                </div>
              ))}
          </div>
        </motion.div>
      </BaseCard>
    </div>
  )
}
