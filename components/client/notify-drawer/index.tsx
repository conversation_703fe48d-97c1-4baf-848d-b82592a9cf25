import { useTranslate } from '@tolgee/react'
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'antd'
import { useMemo, useRef } from 'react'
import Notice from '@/public/svg/essetional/notice.svg'
import CloseIcon from '@/public/svg/essetional/close.svg'
// import ServiceIcon from '@/public/svg/essetional/service.svg'
import EmailIcon from '@/public/svg/essetional/email.svg'
import RightLinear from '@/public/svg/arrow/right_linear.svg'
import { useUserStore } from '@/store/user'
import SegmentedTab from '../segmented-tab'
import useNotifyDrawer from '@/hooks/useNotifyDrawer'
import { SegmentedTabOption } from '@libs/typings/src'
import { cn, dayjs } from '@libs/utils/src'
import NotifyItem from './components/notify-item'
import { NotifyTabsKeysEnum } from '@/enums/user'
import DelayModal from '../delay-modal'
import NoDataImage from '../no-data-image'
import Image from 'next/image'

import './index.css'
import { Radio, Message } from 'iconsax-react'
import useSWR from 'swr'
import { useDbStore } from '@/store/db'
import { createApi } from '@libs/apis/src'
import { openThirdPartyUrl } from '@/utils/open-page'

export default function NotifyDrawer() {
  const { t } = useTranslate()
  const segmentedTabRef = useRef<HTMLDivElement | null>(null)
  const { notifyModalOpen, setNotifyModalOpen, notifyDetail, setNotifyDetail } = useUserStore()
  const {
    notifyInfo,
    announcementList,
    noticeList,
    marqueeList,
    notifyTabActiveKey,
    setNotifyTabActiveKey,
    setAllNoticesRead,
    setAllAnnouncementsRead,
    removeAllNoticeRead,
    // removeAllAnnouncementsRead,
    getAnnouncement,
    getNotice,
    getNewNotice,
    drawerWidth,
    setDrawerWidth,
    setAnnouncementRead,
    setNoticeRead,
    getAnnouncementLoading,
    getNoticeLoading,
    getMarqueeLoading,
  } = useNotifyDrawer()

  const notifyTabs = useMemo<SegmentedTabOption<NotifyTabsKeysEnum>[]>(
    () => [
      {
        value: NotifyTabsKeysEnum.CustomerService,
        label: t('notify.customer_service', {}),
        children: (
          <div className="flex flex-col gap-2">
            <CustomerService />
          </div>
        ),
      },
      {
        value: NotifyTabsKeysEnum.Announcement,
        label: (
          <div className="flex h-[38px] w-full items-center">
            <Badge className="w-full leading-6" size="small" count={notifyInfo?.announcement}>
              <div className="text-sm text-inherit">{t('notify.announcement', {})}</div>
            </Badge>
          </div>
        ),
        children: (
          <Spin spinning={getAnnouncementLoading} wrapperClassName="h-full">
            <div className="flex flex-col gap-2">
              <div className="flex justify-end gap-3 text-xs font-semibold text-bgs-900">
                <span
                  className="cursor-pointer"
                  onClick={() => {
                    setAllAnnouncementsRead()
                    getAnnouncement()
                    getNewNotice()
                  }}
                >
                  {t('notify.all_read', {})}
                </span>
                {/* <span
                  className="cursor-pointer"
                  onClick={() => {
                    removeAllAnnouncementsRead()
                    getAnnouncement()
                    getNewNotice()
                  }}
                >
                  {t('notify.delete_read', {})}
                </span> */}
              </div>
              {announcementList?.map((e) => (
                <NotifyItem
                  key={e.id}
                  className="cursor-pointer"
                  onClick={() => {
                    setAnnouncementRead({ id: e.id.toString() })
                    getAnnouncement()
                    getNewNotice()
                    setNotifyDetail?.({
                      open: true,
                      info: e,
                    })
                  }}
                  dot={!e?.isRead}
                  icon={<NotifyTypeIcon type={NotifyTabsKeysEnum.Announcement} />}
                  title={e?.title}
                  value={dayjs(e?.startTime).format('YYYY-MM-DD HH:mm:ss')}
                  linkIcon={
                    <div>
                      <RightLinear className="h-[18px] w-[18px] cursor-pointer"></RightLinear>
                    </div>
                  }
                ></NotifyItem>
              ))}
              <NoDataImage
                show={!getAnnouncementLoading && !Boolean(announcementList?.length)}
                size={[120, 90]}
                className="gap-4"
              ></NoDataImage>
            </div>
          </Spin>
        ),
      },
      {
        value: NotifyTabsKeysEnum.Notification,
        label: (
          <Badge size="small" count={notifyInfo?.notice}>
            <div className="text-sm">{t('notify.notification', {})}</div>
          </Badge>
        ),
        children: (
          <Spin spinning={getNoticeLoading} wrapperClassName="h-full">
            <div className="flex flex-col gap-2">
              <div className="flex justify-end gap-3 text-xs font-semibold text-bgs-900">
                <span
                  className="cursor-pointer"
                  onClick={() => {
                    setAllNoticesRead()
                    getNotice()
                    getNewNotice()
                  }}
                >
                  {t('notify.all_read', {})}
                </span>
                <span
                  className="cursor-pointer"
                  onClick={() => {
                    removeAllNoticeRead()
                    getNotice()
                    getNewNotice()
                  }}
                >
                  {t('notify.delete_read', {})}
                </span>
              </div>
              {noticeList?.map((e) => (
                <NotifyItem
                  className="cursor-pointer"
                  onClick={() => {
                    setNoticeRead({ id: e.id.toString() })
                    getNotice()
                    getNewNotice()
                    setNotifyDetail?.({
                      open: true,
                      info: e,
                    })
                  }}
                  key={e.id}
                  id={e.id}
                  dot={!e?.isRead}
                  iconClassName={e?.isRead ? 'bg-bgs-600' : 'bg-shades-100'}
                  icon={
                    <NotifyTypeIcon
                      type={NotifyTabsKeysEnum.Notification}
                      className={e?.isRead ? 'text-bgs-900' : 'text-primary-1'}
                    />
                  }
                  title={e?.title}
                  value={dayjs(e?.startTime).format('YYYY-MM-DD HH:mm:ss')}
                  linkIcon={
                    <div>
                      <RightLinear className="h-[18px] w-[18px] cursor-pointer"></RightLinear>
                    </div>
                  }
                ></NotifyItem>
              ))}
              <NoDataImage
                show={!getNoticeLoading && !Boolean(noticeList?.length)}
                size={[120, 90]}
                className="gap-4"
              ></NoDataImage>
            </div>
          </Spin>
        ),
      },
      {
        value: NotifyTabsKeysEnum.Marquee,
        label: t('notify.marquee', {}),
        children: (
          <Spin spinning={getMarqueeLoading} wrapperClassName="h-full">
            <div className="flex flex-col gap-2">
              {marqueeList?.map((e) => (
                <NotifyItem
                  className="cursor-pointer"
                  key={e.id}
                  onClick={() => {
                    setNotifyDetail?.({
                      open: true,
                      info: { ...e, title: e.content, content: '' },
                    })
                  }}
                  icon={<NotifyTypeIcon type={NotifyTabsKeysEnum.Marquee} />}
                  title={e?.content}
                  value={dayjs(e?.startTime).format('YYYY-MM-DD HH:mm:ss')}
                  linkIcon={
                    <div>
                      <RightLinear className="h-[18px] w-[18px] cursor-pointer"></RightLinear>
                    </div>
                  }
                ></NotifyItem>
              ))}
              <NoDataImage
                show={!getMarqueeLoading && !Boolean(marqueeList?.length)}
                size={[120, 90]}
                className="gap-4"
              ></NoDataImage>
            </div>
          </Spin>
        ),
      },
    ],
    [
      announcementList,
      marqueeList,
      noticeList,
      notifyInfo,
      getAnnouncementLoading,
      getNoticeLoading,
      getMarqueeLoading,
    ],
  )
  const afterOpenChange = () => {
    setDrawerWidth((segmentedTabRef.current?.clientWidth || 0) + 32.02)
  }
  return (
    <ConfigProvider
      theme={{
        token: { colorBgContainer: 'rgba(0,0,0,0)' },
        components: {
          Drawer: {
            colorBgMask: 'var(--color-bgs-100)',
            colorBgElevated: 'var(--color-shades-0)',
            paddingLG: 16,
          },
          Segmented: {
            itemColor: 'var(--color-bgs-900)',
          },
          Badge: {
            colorText: 'initial',
            paddingXS: 3,
          },
          Spin: {
            colorBgContainer: 'rgba(0,0,0,0)',
          },
          Button: {
            controlHeightLG: 44,
            fontSizeLG: 16,
            fontWeight: 400,
          },
        },
      }}
    >
      <>
        <Drawer
          rootClassName="notify-drawer"
          width={drawerWidth}
          afterOpenChange={afterOpenChange}
          title={
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-1">
                <Notice
                  className="h-5 w-5 text-shades-100"
                  onClick={() => {
                    setNotifyModalOpen(true)
                  }}
                />
                <span className="text-lg">{t('notify.title', {})}</span>
              </div>

              <CloseIcon
                className="h-5 w-5 cursor-pointer text-bgs-900"
                onClick={() => {
                  setNotifyModalOpen(false)
                }}
              />
            </div>
          }
          closable={false}
          open={notifyModalOpen}
          onClose={() => setNotifyModalOpen(false)}
          footer={null}
        >
          <div className="flex h-full" ref={segmentedTabRef}>
            <SegmentedTab<NotifyTabsKeysEnum>
              block
              classnames={{
                container: 'flex flex-col',
                content: 'flex-1 overflow-auto',
              }}
              value={notifyTabActiveKey}
              onTabChange={(val) => {
                setNotifyTabActiveKey(val)
              }}
              options={notifyTabs}
              className="bg-gs-200 p-2"
            ></SegmentedTab>
          </div>
        </Drawer>

        <DelayModal open={notifyDetail.open}>
          <Modal
            open={notifyDetail.open}
            title={notifyDetail.info?.title}
            width={442}
            onCancel={() => {
              setNotifyDetail({
                open: false,
                info: null,
              })
            }}
            footer={
              <div className="flex gap-4">
                {/* <Button type="default" size="large" className="flex-1">
                  {t('notify.delete', {})}
                </Button> */}
                <Button
                  type="primary"
                  size="large"
                  className="flex-1"
                  onClick={() => {
                    setNotifyDetail({
                      open: false,
                      info: null,
                    })
                  }}
                >
                  {t('notify.got_it', {})}
                </Button>
              </div>
            }
          >
            <div className="flex flex-col gap-4">
              <div className="text-sm text-bgs-800">
                {dayjs(notifyDetail.info?.startTime).format('YYYY-MM-DD HH:mm:ss')}
              </div>
              <div dangerouslySetInnerHTML={{ __html: notifyDetail.info?.content || '' }}></div>
            </div>
          </Modal>
        </DelayModal>
      </>
    </ConfigProvider>
  )
}

export function NotifyTypeIcon({ type, className }: { type: NotifyTabsKeysEnum; className?: string }) {
  const classNames = cn('h-5 w-5 text-primary-1', className)

  switch (type) {
    case NotifyTabsKeysEnum.CustomerService:
      return <Image src="/img/customer-user.png" alt="客服" width={40} height={40} />
    case NotifyTabsKeysEnum.Marquee:
    case NotifyTabsKeysEnum.HallPopup:
      return <Radio className={classNames} color="currentColor" variant="Bold" />
    case NotifyTabsKeysEnum.Notification:
      return <EmailIcon className={classNames} />
    default:
      return <Message className={classNames} color="currentColor" variant="Bold" />
  }
}

function CustomerService() {
  const { t } = useTranslate()
  const { data: serviceList, isLoading } = useSWR('fetch', async () => {
    const { data } = await createApi().apis.system.getCustomerServiceList({
      data: {},
    })
    return data
  })
  const { openCustomerService } = useDbStore()

  const handleClickServiceItem = (item: { linkUrl: string }) => () => {
    if (!item?.linkUrl) {
      return
    }

    openThirdPartyUrl({ type: 'link', data: item?.linkUrl })
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center">
        <Spin />
      </div>
    )
  }

  return (
    <>
      <NotifyItem
        icon={<NotifyTypeIcon type={NotifyTabsKeysEnum.CustomerService} />}
        iconClassName="w-[40px] h-[40px]"
        title={t('notify.notify_drawer_key0', {})}
        value={t('notify.notify_drawer_key1', {})}
        onClick={openCustomerService}
      />

      {Boolean(serviceList?.length) && (
        <div className="card mt-2 justify-between rounded-2xl bg-bgs-500 px-4 py-2">
          <div className="mb-2 font-semibold text-gs-900">
            <div className="text-base leading-8 text-gs-900">{t('notify.notify_drawer_key0', '7x24')}</div>
          </div>

          <div className="grid grid-cols-1 items-center gap-4">
            {serviceList?.map((item, index) => (
              <div
                className="flex cursor-pointer items-center gap-2 rounded-full bg-shades-0 px-4 py-2"
                key={`${item.name}-${index}`}
                onClick={handleClickServiceItem(item)}
              >
                {item.iconUrl && (
                  <Image className="h-6 w-6 rounded-full" src={item.iconUrl} width={24} height={24} alt={item.name} />
                )}
                <span className="break-all text-xs">{item.name}</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </>
  )
}
