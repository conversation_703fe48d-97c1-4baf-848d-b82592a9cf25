'use client'

import { useState, useCallback, useEffect } from 'react'
import { FilterProvider, useFilter } from './context/filter'
import { useInviteeList } from '../../shared/hooks/useInviteeList'
import { Button, Table } from 'antd'
import { AGENT_MODEL, CodeDetail } from '@libs/typings/src'
import { ColumnsType } from 'antd/es/table'
import {
  PyramidAgentInvitee,
  PyramidAgentGetTheirInviteUsersParams,
  Products,
  createApi,
  UpdateCenterCodeRatioParams,
} from '@libs/apis/src'
import { dayjs, exportToExcel } from '@libs/utils'
import { useParams, useRouter } from 'next/navigation'
import { Input } from 'antd'
import SearchIcon from '@/public/svg/essetional/search.svg'
import ClosePopup from 'public/svg/essetional/close_popup.svg'
import FilterModal from '@/app/[lng]/agent/[theirInviteId]/components/filter-modal'
import { getIcon } from '@/utils/getIcon'
import CodeDict from '@/components/client/code-dicts'
import PyramidSetRatioModal from '@/app/[lng]/agent/components/pyramid-set-ratio-modal'
import ExportButton from '@/app/[lng]/agent/components/export-buttons'
import { useStaticTools } from '@/store/static-tools'
import { useTranslate } from '@tolgee/react'
import EmptyContent from '@/components/client/empty-content'

/**
 * 整体实现思路：
 * 1. 复用theirInviteId中的SearchComponent和FilterModal组件
 * 2. 创建自定义的SearchComponent，模仿theirInviteId的search组件样式和功能
 * 3. 创建一个兼容FilterModal组件的filterParams对象
 * 4. 在useInviteeList钩子中传入所有筛选参数，使用API的筛选功能
 */

// 自定义搜索组件，模仿theirInviteId的search组件
function SearchComponent({
  isLoading,
  searchValue,
  onChange,
  onOpenFilter,
}: {
  isLoading: boolean
  searchValue: string
  onChange: (value: string) => void
  onOpenFilter: () => void
}) {
  const { t } = useTranslate()
  return (
    <div className="flex w-full items-center justify-between">
      <Input
        className="w-[200px]"
        size="large"
        prefix={<SearchIcon className="h-[18px] w-[18px] text-bgs-900" />}
        placeholder={t('agent.search_user_uid', {})}
        allowClear={{
          clearIcon: <ClosePopup className="h-[18px] w-[18px] text-bgs-900" />,
        }}
        value={searchValue}
        onChange={(e) => onChange(e.target.value)}
        maxLength={30}
        disabled={isLoading}
      />
      <div className={`cursor-pointer ${isLoading ? 'pointer-events-none' : ''}`} onClick={onOpenFilter}>
        {getIcon('filterIcon', { className: 'w-7 h-7 text-bgs-900' })}
      </div>
    </div>
  )
}

// 实际的表格内容组件
function InvitationDetailsContent() {
  const { t } = useTranslate()
  const { message } = useStaticTools()
  const router = useRouter()
  const { updateAndApplyFilter } = useFilter()
  const [openFilter, setOpenFilter] = useState(false)
  const [searchValue, setSearchValue] = useState('')
  // 添加状态用于编辑佣金比例
  const [pyramidSetRatioModal, setPyramidSetRatioModal] = useState(false)
  const [saveLoading, setSaveLoading] = useState(false)
  const [productRatioLoading, setProductRatioLoading] = useState(false)
  const [productRatio, setProductRatio] = useState<Products[]>([])
  const [singleRatio, setSingleRatio] = useState<UpdateCenterCodeRatioParams | null>(null)

  // 添加状态存储筛选条件
  const [teamNumMin, setTeamNumMin] = useState<string | undefined>(undefined)
  const [teamNumMax, setTeamNumMax] = useState<string | undefined>(undefined)
  const [startTime, setStartTime] = useState<number | undefined>(undefined)
  const [endTime, setEndTime] = useState<number | undefined>(undefined)
  const { lng } = useParams()
  /**
   * 创建一个本地filterParams对象，兼容FilterModal组件
   * 注意：
   * 1. 添加了'uid'字段作为占位符，满足PyramidAgentGetTheirInviteUsersParams类型需求
   * 2. 包含了teamNumMin、teamNumMax、startTime、endTime等筛选字段
   */
  const [filterParams, setFilterParams] = useState({
    model: AGENT_MODEL.PYRAMID,
    pageNum: 1,
    pageSize: 10,
    uid: 'placeholder', // 添加一个占位符uid，满足类型要求
    searchUid: '',
    teamNumMin: 0,
    teamNumMax: 0,
    startTime: 0,
    endTime: 0,
  })

  /**
   * 使用共享钩子获取数据
   * 传递完整的筛选参数给API
   */
  const { inviteeList, isLoading, refresh } = useInviteeList({
    pageNum: 1, // 固定为第1页
    pageSize: 100, // 固定获取100条数据
    model: AGENT_MODEL.PYRAMID,
    searchUid: searchValue,
    // 添加API支持的筛选参数
    teamNumMin: teamNumMin,
    teamNumMax: teamNumMax,
    startTime: startTime,
    endTime: endTime,
    apiType: 'detail', // 使用 getInviteDetail API
  } as any) // 使用类型断言解决类型检查错误

  /**
   * 处理筛选条件变化，触发数据刷新
   * 使用useEffect监听筛选条件变化
   */
  useEffect(() => {
    refresh()
  }, [searchValue, teamNumMin, teamNumMax, startTime, endTime, refresh])

  /**
   * 处理搜索值变化
   * 1. 更新本地searchValue状态
   * 2. 更新filterParams中的searchUid
   * 3. 更新useFilter上下文中的searchUid
   */
  const handleSearchChange = (value: string) => {
    setSearchValue(value)
    // 同时更新filterParams，保持UI一致性
    setFilterParams((prev) => ({ ...prev, searchUid: value }))
    // 更新filter context
    updateAndApplyFilter({ searchUid: value })
  }

  /**
   * 获取产品佣金比例信息
   */
  const getProductRatio = useCallback(async () => {
    try {
      setProductRatioLoading(true)
      const { data } = await createApi().apis.agent.getProductRatio()
      setProductRatio(data?.products || [])
    } catch (error) {
      console.error('获取产品佣金比例失败', error)
    } finally {
      setProductRatioLoading(false)
    }
  }, [])

  /**
   * 处理单个佣金比例设置
   */
  const handleSetSingleRatio = (params: UpdateCenterCodeRatioParams) => {
    setSingleRatio(params)
    // 确保有产品比例数据
    if (productRatio.length === 0) {
      getProductRatio()
    }
  }

  /**
   * 处理佣金比例变化
   */
  const handleRatioChange = (value: number, product: Products, maxProductRatio: number) => {
    if (!singleRatio) return

    const newRebateRatio = singleRatio.rebateRatio.map((item) => {
      if (item.productCd === product.productCd) {
        return {
          ...item,
          selfRatio: value,
          childRatio: Math.max(0, maxProductRatio - value),
        }
      }
      return item
    })

    setSingleRatio({
      ...singleRatio,
      rebateRatio: newRebateRatio,
    })
  }

  /**
   * 保存佣金比例设置
   */
  const handleSaveSetting = async () => {
    if (!singleRatio) return

    try {
      setSaveLoading(true)
      await createApi().apis.agent.updateCenterCodeRatio({
        data: singleRatio,
      })
      message.success(t('agent.setting', '佣金比例设置成功'))
      setPyramidSetRatioModal(false)
      refresh() // 刷新列表数据
    } catch (error) {
      console.error('设置佣金比例失败', error)
      message.error(t('agent.setting_fail', '设置佣金比例失败'))
    } finally {
      setSaveLoading(false)
    }
  }

  /**
   * 处理筛选提交
   * 1. 从filterParams获取筛选值并更新到本地状态
   * 2. 关闭筛选弹窗
   * 3. 触发数据刷新
   */
  const handleSubmitSearch = useCallback(() => {
    // 获取筛选条件并更新本地状态
    setSearchValue(filterParams.searchUid)

    // 更新筛选状态
    setTeamNumMin(filterParams.teamNumMin > 0 ? String(filterParams.teamNumMin) : undefined)
    setTeamNumMax(filterParams.teamNumMax > 0 ? String(filterParams.teamNumMax) : undefined)
    setStartTime(filterParams.startTime > 0 ? filterParams.startTime : undefined)
    setEndTime(filterParams.endTime > 0 ? filterParams.endTime : undefined)

    // 更新filter context - 仅传递支持的参数
    updateAndApplyFilter({
      searchUid: filterParams.searchUid,
    })

    // 关闭筛选弹窗
    setOpenFilter(false)

    // 显示筛选成功提示
    if (
      filterParams.teamNumMin > 0 ||
      filterParams.teamNumMax > 0 ||
      filterParams.startTime > 0 ||
      filterParams.endTime > 0
    ) {
      message.success(t('agent.filterApplied', '筛选条件已应用'))
    }
  }, [filterParams, updateAndApplyFilter, message, t])

  // 金字塔代理特定的列定义
  const columns: ColumnsType<PyramidAgentInvitee> = [
    {
      title: t('agent.uid', 'UID'),
      dataIndex: 'uid',
      key: 'uid1',
      align: 'center',
    },
    {
      title: t('agent.number_of_invites', 'Number of Invites'),
      dataIndex: 'inviteNum',
      key: 'inviteNum',
      align: 'center',
      sorter: (a, b) => Number(a.inviteNum || 0) - Number(b.inviteNum || 0),
    },
    {
      title: t('agent.team_members', 'Team Members'),
      dataIndex: 'teamNum',
      key: 'teamNum',
      align: 'center',
      sorter: (a, b) => Number(a.teamNum) - Number(b.teamNum),
    },
    {
      title: t('agent.pyramid_commission_ratio', 'Pyramid Commission Ratio'),
      dataIndex: 'productRebateList',
      key: 'productRebateList',
      align: 'center',
      render: (productRebateList, record) => {
        return (
          <div className="flex items-center justify-center gap-2">
            <div className="flex flex-col gap-1">
              {productRebateList?.map((item: Products) => (
                <div className="flex items-center text-sm" key={item.productCd}>
                  <CodeDict type={CodeDetail.AgentProductCd} textId={item.productCd} />: {t('agent.me', 'Me')}{' '}
                  {item.selfRatio || 0}% / {t('agent.friend', 'Friend')} {item.childRatio || 0}%
                </div>
              ))}
            </div>
            <div
              className="cursor-pointer text-primary-1"
              onClick={() => {
                handleSetSingleRatio({
                  uid: record.uid,
                  rebateRatio:
                    record?.productRebateList?.map((ratio) => ({
                      productCd: ratio.productCd || '',
                      selfRatio: ratio.selfRatio || 0,
                      childRatio: ratio.childRatio || 0,
                    })) || [],
                })
                setPyramidSetRatioModal(true)
              }}
            >
              {getIcon('edit', {
                className: 'h-4 w-4 text-primary-1',
              })}
            </div>
          </div>
        )
      },
    },
    {
      title: t('agent.registration_time', 'Registration Time'),
      dataIndex: 'registerDate',
      key: 'registerDate',
      align: 'center',
      sorter: (a, b) => Number(dayjs(a.registerDate).valueOf()) - Number(dayjs(b.registerDate).valueOf()),
      render: (value) => <div className="text-xs">{dayjs(value).format('YYYY-MM-DD HH:mm:ss')}</div>,
    },
    {
      title: t('agent.action', 'Action'),
      dataIndex: '-',
      key: '-',
      align: 'center',
      render: (_, record) => (
        <Button
          className="text-xs"
          type="link"
          size="large"
          onClick={() => router.push(`/agent/${record.uid}?mode=pyramid`)}
        >
          {t('agent.details', 'Details')}
        </Button>
      ),
    },
  ]

  const downloadXlsx = async () => {
    if (!inviteeList?.length) {
      message.error(t('common.empty', '没有可导出的数据'))
    } else {
      const { data: resData } = await createApi().apis.common.getCodeDetailList({
        data: {
          lanType: lng as string,
          codeVal: CodeDetail.AgentProductCd,
        },
      })
      exportToExcel(
        inviteeList.map((item: any) => ({
          UID: item.uid,
          [t('agent.number_of_invites', 'Number of Invites')]: item.inviteNum || '',
          [t('agent.team_members', 'Team Members')]: item.teamNum || '',
          [t('agent.pyramid_commission_ratio', 'Pyramid Commission Ratio')]: item.productRebateList
            ?.map(
              (rebate: any) =>
                `${resData?.find((cn) => cn.codeVal === rebate.productCd)?.codeKey || ''}: Me${rebate?.selfRatio || 0}%/Friend${rebate?.childRatio || 0}%`,
            )
            .join(','),
          [t('agent.registration_time', 'Registration Time')]: dayjs(item.registerDate).format('YYYY-MM-DD HH:mm:ss'),
        })),
        t('agent.pyramid_report', {}),
      )
    }
  }

  return (
    <div className="rounded-3xl bg-bgs-100 bg-opacity-70 p-6">
      {/* 搜索组件 */}
      <SearchComponent
        isLoading={isLoading}
        searchValue={searchValue}
        onChange={handleSearchChange}
        onOpenFilter={() => setOpenFilter(true)}
      />

      {/* 表格组件 */}
      <Table
        className="mt-4 w-full"
        rowKey="uid"
        columns={columns}
        dataSource={inviteeList}
        loading={isLoading}
        pagination={false} // 禁用分页功能
        scroll={{ x: 1200 }}
        locale={{
          emptyText: <EmptyContent />,
        }}
      />

      <ExportButton
        context={t(
          'agent.export_context',
          'The invitation details include agent invitations and direct referrals. For a detailed transaction history, please click "Export".',
        )}
        downloadXlsx={downloadXlsx}
      />

      {/* 筛选弹窗组件 - 复用theirInviteId的FilterModal组件 */}
      <FilterModal
        histInviteeLoading={isLoading}
        openFilter={openFilter}
        filterParams={filterParams as PyramidAgentGetTheirInviteUsersParams}
        onClose={() => setOpenFilter(false)}
        setFilterParams={setFilterParams as any}
        handleSubmitSearch={handleSubmitSearch}
      />

      {/* 金字塔佣金比例设置的模态框组件 */}
      <PyramidSetRatioModal
        saveLoading={saveLoading}
        openPyramidSetRatioModal={pyramidSetRatioModal}
        productRatio={productRatio}
        singleRatio={singleRatio}
        productRatioLoading={productRatioLoading}
        setPyramidSetRatioModal={setPyramidSetRatioModal}
        onRatioChange={handleRatioChange}
        onSaveSetting={handleSaveSetting}
        isCenter={true}
      />
    </div>
  )
}

// 入口组件，提供筛选上下文
export default function InvitationDetails() {
  return (
    <FilterProvider>
      <InvitationDetailsContent />
    </FilterProvider>
  )
}
