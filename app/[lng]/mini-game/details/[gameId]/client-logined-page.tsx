'use client'

import MiniGameContainer from './components/mini-game-container'
import {
  useMiniGameButtons,
  useMiniGameDispatch,
  useMiniGameSettings,
  useMiniRealLink,
  useMiniSharedMedia,
} from './hooks/useMiniGame'
import { useIframe } from './hooks/useIframe'
import { cn } from '@libs/utils/src'
import MiniGameShared from './components/mini-game-shared'
import { useHotGames } from './hooks/useHotGames'
import GameList from '../../../home/<USER>/home/<USER>'
import { useProviders } from './hooks/useProviders'
import { useTranslate } from '@tolgee/react'
import MiniGameWarningModal from './components/mini-game-warning-modal'

export type MiniGameLoginedProps = {
  lng: string
  gameId: string | undefined
  groupId: string
}

export default function MiniGameLoginedPage({ lng, gameId, groupId }: MiniGameLoginedProps) {
  const { t } = useTranslate()
  const {
    realLinkLoading,
    gameDetailsLoading,
    gameDetails,
    miniGameRealLink,
    walletInfo,
    coin,
    gameDetailsReload,
    reloadRealLink,
  } = useMiniRealLink({
    lng,
    gameId,
    groupId,
  })

  const { miniGameButtons } = useMiniGameButtons(gameDetails)
  const { copyUrl, sharedList, openModal, handleModal, openApp } = useMiniSharedMedia(gameId, groupId, lng)
  const {
    theater,
    showMiniGameScreen,
    gameUrl,
    fullScreen,
    showTryMode,
    showWarningPopup,
    handleButtonClick,
    handleSettingClick,
    setFullScreen,
    setShowWarningPopup,
  } = useMiniGameDispatch({
    lng,
    coin,
    miniGameUrl: miniGameRealLink!,
    gameDetails,
    walletInfo,
    gameDetailsReload,
    handleModal,
    reloadGameLink: reloadRealLink,
  })
  const { miniGameSetting, showHighlight, setShowHighlight } = useMiniGameSettings(theater, gameDetails)
  const { iframePosition, iframeContainerRef, iframeOnLoad } = useIframe(theater, fullScreen, setFullScreen)
  const { hostGameList } = useHotGames()
  const { providerList } = useProviders()

  return (
    <>
      <MiniGameContainer
        showMiniGameScreen={showMiniGameScreen}
        gameDetailsLoading={gameDetailsLoading}
        linkLoading={realLinkLoading}
        gameDetails={gameDetails}
        miniGameButtons={miniGameButtons}
        theater={theater}
        miniGameSettings={miniGameSetting}
        iframePosition={iframePosition}
        gameUrl={gameUrl}
        iframeContainerRef={iframeContainerRef}
        showTryMode={showTryMode}
        showHighlight={showHighlight}
        setShowHighlight={setShowHighlight}
        handleButtonClick={handleButtonClick}
        handleSettingClick={handleSettingClick}
        iframeOnLoad={iframeOnLoad}
      />
      <div className={cn('ml-auto mr-auto mt-8', theater ? 'max-w-[1200px]' : '')}>
        <GameList code={'hot'} title={t('mini_game.recom_game', {})} dataList={hostGameList} type="gameList" />
      </div>
      <div className={cn('ml-auto mr-auto mt-8', theater ? 'max-w-[1200px]' : '')}>
        <GameList code={'Baccarat'} title={t('mini_game.game_provider', {})} dataList={providerList} type="providers" />
      </div>
      <MiniGameShared
        copyUrl={copyUrl}
        isModalOpen={openModal}
        sharedList={sharedList}
        handleModal={handleModal}
        openApp={openApp}
      />
      <MiniGameWarningModal
        showWarningPopup={showWarningPopup}
        gameDetails={gameDetails}
        setShowWarningPopup={setShowWarningPopup}
      />
    </>
  )
}
