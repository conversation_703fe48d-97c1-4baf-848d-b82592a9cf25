@import './scroll-bar.css';
@import './animation.css';

@font-face {
  font-family: Saira;
  src: url(./fonts/saria.ttf) format('truetype');
  font-display: swap;
}

@font-face {
  font-family: <PERSON>ra Fallback;
  src: local('Arial');
  ascent-override: 109.56%;
  descent-override: 42.38%;
  line-gap-override: 0%;
  size-adjust: 103.59%;
}

html {
  height: auto;
  background-color: var(--color-shades-0) !important;
}

body {
  height: 100%;
  background-color: var(--color-shades-0) !important;
  /* min-width: 960px; */
  font-size: 14px;

  img {
    /*
    * prevent img highlight
    */
    user-select: none;
  }
}

.ant-app {
  min-height: 100vh;
  background-color: var(--color-shades-0) !important;
  display: flex;
}

.ant-layout {
  height: 100%;
  flex: 1;
}

/** dashed border */
.dashed-border {
  border-bottom: 1px dashed var(--text_color_02);
  user-select: none;
}

.popup-wrap {
  @apply rounded p-4;
  /* TODO: 待修改 */
  background-color: var(--color-bg-popup);
  box-shadow: 0 2px 8px 0 rgb(0 0 0 / 15%);
}

/* 去除浏览器自动填充背景色 */
input:-webkit-autofill {
  -webkit-text-fill-color: var(--nc-01);
  transition: background-color 5000s ease-in-out 0s;
}

.ant-select-selector {
  border: none !important;
}
body {
  .ant-input-number-affix-wrapper .ant-input-number-prefix,
  .ant-input-number-affix-wrapper .ant-input-number-suffix {
    pointer-events: all;
  }
}

@keyframes run {
  0% {
    transform: translateY(0) translateX(0);
  }

  100% {
    transform: translateY(120vh) translateX(-500px);
  }
}

.red-envelope-btn {
  width: 90%;
  background: linear-gradient(180deg, #fdf8e6 0%, #ffc987 100%);
  border: 1.5px solid #ffe7cd;
  border-radius: 20px;
  box-shadow:
    0 3px 4px 0 rgb(236 170 88 / 60%) inset,
    0 4px 4px 0 rgb(186 38 42 / 60%);
}

.custom-toast-modal .ant-modal-confirm-title {
  font-size: 18px;
  font-weight: 700;
  color: var(--color-shades-100) !important;
  text-align: center;
  width: 100%;
  margin-bottom: 0;
}

.dot-color .ant-slider .ant-slider-handle::after {
  background: linear-gradient(180deg, #ffffff 29%, #b8d9ff 100%);
}

.global-message {
  .ant-message-notice-content {
    @apply border border-solid border-bgs-600 bg-bgs-200;
  }
}
.global-radio {
  &.ant-radio-group {
    .ant-radio {
      --ant-radio-radio-color: var(--color-shades-0);
    }
    .ant-radio-button-wrapper {
      --ant-radio-button-solid-checked-bg: var(--color-gs-200);
      --ant-radio-button-solid-checked-hover-bg: var(--color-gs-200);
      --ant-radio-button-color: var(--color-bgs-900);
      border-radius: var(--ant-border-radius);
      border: 0;
      &:hover {
        border: 0;
      }
    }
    .ant-radio-button-wrapper:not(:first-child)::before {
      width: 0;
    }
    .ant-radio-button-wrapper:hover {
      color: var(--color-shades-100);
      background-color: var(--color-gs-200);
    }
  }
}
/* .global-checkbox {
  .ant-checkbox-checked .ant-checkbox-inner {
    @apply border-success-3 bg-success-3;
  }
} */

w3m-modal {
  z-index: 2030 !important;
}

.no-asterisk .ant-form-item-required::before {
  display: none !important;
}

/* Chrome自动填充样式定制 */
input:-webkit-autofill,
textarea:-webkit-autofill,
select:-webkit-autofill {
  -webkit-text-fill-color: var(--color-shades-100) !important;
  -webkit-box-shadow: 0 0 0px 1000px var(--color-shades-0) inset !important;
  font-weight: 500;
}

input:-webkit-autofill:hover,
textarea:-webkit-autofill:hover,
select:-webkit-autofill:hover,
input:-webkit-autofill:focus,
textarea:-webkit-autofill:focus,
select:-webkit-autofill:focus {
  -webkit-box-shadow: 0 0 0px 1000px var(--color-gs-600) inset !important;
}

/* 保持聚焦状态样式一致 */
input:focus:-webkit-autofill {
  -webkit-text-fill-color: var(--color-shades-100) !important;
}

.slider-first-button-bg {
  background-image: url('/img/slide-bar-1.png') !important;
  background-repeat: no-repeat;
  background-size: cover !important;
  background-position: center;
}

.slider-second-button-bg {
  background-image: url('/img/slide-bar-2.png') !important;
  background-repeat: no-repeat;
  background-size: cover !important;
  background-position: center;
}

.ant-input-data-count {
  font-size: 12px;
  color: var(--color-gs-800);
}

.flex-table {
  .ant-table-thead .ant-table-column-has-sorters .ant-table-column-sorters {
    justify-content: flex-start !important;
    .ant-table-column-title {
      flex: none;
    }
  }

  .ant-table-thead .ant-table-cell[style*='text-align: right'] .ant-table-column-sorters {
    justify-content: flex-end !important;
  }
}

.betting-record-popover {
  .ant-popover-inner {
    background: var(--color-bgs-400) !important;
    border-radius: 8px;
    padding: 8px 16px;
  }
  .ant-popover-arrow::before {
    background: var(--color-bgs-400) !important;
  }
}

.ant-table {
  background: transparent !important;
}
/* 表头样式 */
.ant-table-thead > tr > th {
  font-size: 14px !important;
  font-weight: 500 !important;
}

.ant-table-thead .ant-table-column-has-sorters .ant-table-column-sorters {
  display: flex;
  justify-content: center !important;
}

/* 表格内容样式 */
.ant-table-tbody > tr > td {
  color: var(--color-shades-100) !important;
  font-size: 14px !important;
}
.ant-table-tbody > tr > td:first-child {
  border-radius: 8px 0 0 8px !important;
}
.ant-table-tbody > tr > td:last-child {
  border-radius: 0 8px 8px 0 !important;
}
.ant-table-tbody > tr:nth-child(odd) {
  background: var(--color-bgs-500) !important;
  border-radius: 8px !important;
}

.ant-pagination .ant-pagination-item-active {
  border-radius: 50% !important;
}
.ant-pagination .ant-pagination-item-active a {
  color: var(--color-on-button-primary) !important;
}
.ant-table-wrapper .ant-table-thead th.ant-table-column-sort {
  background: var(--color-shades-0) !important;
}
.ant-table-wrapper .ant-table-thead th.ant-table-column-has-sorters:hover {
  background: var(--color-shades-0) !important;
}
.ant-table-expanded-row-fixed {
  background: var(--color-shades-0) !important;
}
.ant-input:focus {
  border-color: var(--color-primary-1) !important;
}
.ant-input {
  font-size: 1rem !important;
}
.ant-form-item-explain-error {
  font-size: 12px !important;
  margin-top: 2px;
  margin-left: 4px;
}
.fission-record-select .ant-select-selector {
  background: transparent;
}

.custom-countdown .ant-statistic-content {
  color: var(--color-gs-800) !important;
  font-size: 12px !important;
}

.ant-btn-variant-solid:not(:disabled):not(.ant-btn-disabled) {
  background: var(--color-gs-600) !important;
  color: var(--color-on-button-primary) !important;
}

.ant-btn-variant-solid:not(:disabled):not(.ant-btn-disabled):hover {
  background: var(--color-primary-1) !important;
  color: var(--color-on-button-primary) !important;
}

.ant-drawer .ant-drawer-content {
  background: var(--color-shades-0) !important;
}
.ant-segmented-item-selected .ant-badge {
  color: var(--color-on-button-primary) !important;
}

.ant-checkbox-checked .ant-checkbox-inner {
  background-color: var(--color-primary-1) !important;
  border-color: var(--color-primary-1) !important;
}

.ant-btn-color-primary {
  background: var(--color-primary-1) !important;
  border-color: var(--color-primary-1) !important;
  color: var(--color-on-button-primary) !important;
}

.ant-btn-color-primary.ant-btn-variant-link {
  background-color: transparent !important;
  color: var(--color-primary-1) !important;
}

.ant-btn-color-primary.ant-btn-variant-outlined:not(:disabled):not(.ant-btn-disabled):hover,
.ant-btn-color-primary.ant-btn-variant-dashed:not(:disabled):not(.ant-btn-disabled):hover {
  border-color: var(--color-primary-1) !important;
  color: var(--color-primary-1) !important;
}

.ant-switch.ant-switch-checked {
  background-color: var(--color-primary-1) !important;
}
.ant-btn-variant-solid:disabled,
.ant-btn-variant-solid.ant-btn-disabled {
  border-color: var(--color-gs-700) !important;
  color: var(--color-gs-800) !important;
  background: var(--color-gs-700) !important;
}

.ant-btn-variant-outlined {
  background-color: transparent !important;
  &:hover {
    border-color: var(--color-primary-1) !important;
    color: var(--color-primary-1) !important;
  }
}

.anticon {
  color: var(--color-bgs-900) !important;
}
.ant-alert .ant-alert-icon {
  color: var(--color-primary-1) !important;
}
.ant-message .ant-message-notice-wrapper .ant-message-success > .anticon {
  color: var(--color-success-3) !important;
}
.ant-message .ant-message-notice-wrapper .ant-message-error > .anticon {
  color: var(--color-error-3) !important;
}
.ant-message .ant-message-notice-wrapper .ant-message-warning > .anticon {
  color: var(--color-warning-3) !important;
}
.ant-notification .ant-notification-notice-wrapper .ant-notification-notice-icon-success.anticon {
  color: var(--color-success-3) !important;
}
.ant-notification .ant-notification-notice-wrapper .ant-notification-notice-icon-error.anticon {
  color: var(--color-error-3) !important;
}
.ant-notification .ant-notification-notice-wrapper .ant-notification-notice-icon-warning.anticon {
  color: var(--color-warning-3) !important;
}
.ant-radio-group-solid .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
  background-color: var(--color-primary-1) !important;
}
.ant-radio-button-wrapper:hover {
  background-color: var(--color-gs-400);
  color: var(--color-on-button-primary) !important;
}

.modal-table {
  .ant-table-thead > tr > th {
    padding: 10px 16px !important;
  }
}
