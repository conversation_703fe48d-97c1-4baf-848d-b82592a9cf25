import React, { useMemo } from 'react'
import { useTranslate } from '@tolgee/react'
import { Button, Modal } from 'antd'
import { SignInAwarItem } from '@libs/apis/src/interface'

type Props = {
  award: SignInAwarItem
  open: boolean
  onClose: () => void
}

function SignInSuccessModal({ award, open, onClose }: Props) {
  const { t } = useTranslate()

  const modal = useMemo(() => {
    const amount = Number(award?.quantity || 0).toFixed(2)
    const symbol = award?.symbol || ''
    return (
      <div className="flex min-w-[260px] flex-col items-center justify-center rounded-xl bg-white">
        <div className="bg-primary-500 flex w-full justify-center rounded-t-lg px-5 py-3.5 text-white">
          {t('activity.sign_in_successfully')}
        </div>
        <div className="flex w-full flex-col items-center justify-center px-5 pb-5 pt-2">
          {/* <Image src={imgLightGold} alt="icon_image_light_gold.png" width={60} height={80} /> */}

          <div className="flex w-full items-center pb-5 pt-2">
            <span className="mr-2">
              {t('activity.sign_in_successfully')}
              {t('activity.get_daily_reward')}
            </span>
            <span className="text-lg text-green-500">{amount ? `+ ${amount} ${symbol}` : ''}</span>
          </div>
          <Button className="w-full" type="primary" block onClick={onClose}>
            {t('activity.take_it')}
          </Button>
        </div>
      </div>
    )
  }, [award])

  return (
    <Modal title={t('activity.sign_in')} footer={false} open={open} onCancel={onClose}>
      {modal}
    </Modal>
  )
}

export default SignInSuccessModal
