import { getAllowedLanguages } from '@/i18n/shared'
import GameShowsClientPage from './client-page'
import { GameShowHeaderIcon } from './constants'

export default async function Page({
  params,
  searchParams,
}: {
  params: Promise<{ lng: string }>
  searchParams: Promise<{ [key: string]: string }>
}) {
  const routeParams = await params
  const routerQuery = await searchParams
  const data = await getAllowedLanguages()
  const code = (routerQuery?.code as keyof typeof GameShowHeaderIcon) || 'hot'

  return (
    <GameShowsClientPage
      key={routerQuery?.typeCd || routerQuery?.supplierId || routerQuery?.groupId || routerQuery?.tabId || ''}
      code={code}
      title={routerQuery?.title || ''}
      typeCd={routerQuery?.typeCd || ''}
      tabId={routerQuery?.tabId || ''}
      supplierId={routerQuery?.supplierId || ''}
      lng={routeParams?.lng || ''}
      groupId={routerQuery?.groupId || ''}
      lanuages={data.langTypeCds.split(',').map((item) => ({
        label: item,
        value: item,
      }))}
    />
  )
}
