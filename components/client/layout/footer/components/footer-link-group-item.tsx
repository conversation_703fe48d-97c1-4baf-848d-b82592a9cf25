import { Link, LinkGroup } from '@libs/apis/src'
import { useTranslate } from '@tolgee/react'

type Props = {
  linkGroup: LinkGroup
  groupLinkClick: (item: Link) => void
}

export default function FooterLinkGroupItem({ linkGroup, groupLinkClick }: Props) {
  const { t } = useTranslate()
  if (!linkGroup) return null

  return (
    <div className="min-w-[130px] space-y-4">
      <div className="text-sm text-[#364249]">{t(`footer.${linkGroup.groupName?.toLowerCase()}`)}</div>
      <div className="flex flex-row flex-wrap gap-4 xl:flex-col">
        {linkGroup.links.map((item, index) => {
          return (
            <div
              className="inline-flex cursor-pointer items-center space-x-2 text-sm font-medium text-[#8B9BA7] hover:text-white"
              key={item.code + index}
              onClick={() => groupLinkClick(item)}
            >
              {linkGroup.groupName === 'Games' ? item.customName || '' : t(`footer.${item.code}`)}
            </div>
          )
        })}
      </div>
    </div>
  )
}
