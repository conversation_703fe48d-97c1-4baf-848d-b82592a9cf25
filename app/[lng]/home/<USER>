'use client'

import HomeBanner from '@/app/[lng]/home/<USER>/home/<USER>'
// import HomeGame from '@/app/[lng]/home/<USER>/home/<USER>'
// import GlobalSearch from '@/app/[lng]/home/<USER>/home/<USER>'
import Marquee from '@/app/[lng]/home/<USER>/home/<USER>'
import AppDownload from '../../../components/client/app-download'

import { useLayoutStore } from '@/store/layout'
import { uniqBy } from 'lodash-es'

import HallPopup from '@/components/client/hall-popup'
import PromoTaskHallPopup from '@/components/client/promo-task-hall-popup'
import DomainUrlModal from '@/components/client/domain-url-modal'
import { isMerchant920017 } from '@libs/env/src'
import { useAuth } from '@modules/auth/src'
import ActivityShortcut from '@/components/client/activity-shortcut'
import GameSlider, { Game } from '@/components/client/game-slider'
import { useGameShow, useGameShowList } from '../mini-game/game-shows/hooks/useGameShow'
import { GameListItem } from '@libs/apis/src'
import Banner from '@/components/client/banner'
import { useGameGroup } from './components/home/<USER>/useGameList'
import ProviderItem from '@/components/client/provider-item'
import DescriptionCards from './components/home/<USER>'
import GameSection, { GameCard } from './components/home/<USER>'

//for demo
function generateRandomString(length: number) {
  let result = ''
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  const charactersLength = characters.length
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength))
  }
  return result
}

export default function Home() {
  const { layoutData } = useLayoutStore()
  const { components } = layoutData?.dbJson?.page?.home || {}
  const { data: userData } = useAuth()
  const { gameData } = useGameShow({
    typeCd: '',
    lng: 'zh-CN',
    supplierId: '',
    groupId: '360121',
    tabId: '',
  })
  const { gameList } = useGameShowList((gameData?.list as GameListItem[]) || [])
  const { data: gameGroup } = useGameGroup()

  const mapGameItems: Game[] = gameList.map((item) => ({
    id: 'ID',
    name: item.name || generateRandomString(Math.floor(Math.random() * 10) + 1),
    title: generateRandomString(Math.floor(Math.random() * 10) + 1),
    value: Math.floor(Math.random() * 99999) / 10000 + 1,
    imageSrc: item.mobileThumbnail,
  }))

  // Sample game data for sections
  const bingoOriginalsGames: GameCard[] = gameList.slice(0, 8).map((item, index) => ({
    id: `bingo-${index}`,
    name: item.name || 'Bingo Game',
    imageSrc: item.mobileThumbnail,
    category: 'Bingo',
  }))

  const slotsGames: GameCard[] = gameList.slice(8, 16).map((item, index) => ({
    id: `slot-${index}`,
    name: item.name || 'Slot Game',
    imageSrc: item.mobileThumbnail,
    category: 'Slots',
  }))

  const jackpotsGames: GameCard[] = gameList.slice(16, 24).map((item, index) => ({
    id: `jackpot-${index}`,
    name: item.name || 'Jackpot Game',
    imageSrc: item.mobileThumbnail,
    category: 'Jackpots',
  }))
  console.log('gameList by game', gameList)
  const formatComps = components?.map((item) => ({
    ...item,
    title: item.title?.split(':')?.[0],
  }))

  const homeComponents = uniqBy(
    (formatComps || []).concat({
      title: 'game-group',
      style: {},
    }),
    'title',
  ).filter((item) => {
    if (isMerchant920017() && item.title === 'app-download') {
      return false
    }

    return true
  })

  return (
    <div className="relative m-auto w-full text-3xl md:max-w-[1200px]">
      <GameSlider games={mapGameItems} className="mt-8" />
      <Banner className="mt-8" />
      {homeComponents.map((item) => {
        const componentName = item?.title

        switch (componentName) {
          case 'banner':
            return <HomeBanner key={item.title} />
          case 'marquee':
            return <Marquee key={item.title} />
          case 'app-download':
            return <AppDownload key={item.title} position="home" />
          // case 'game-group':
          //   return (
          //     <div key={item.title}>
          //       <GlobalSearch />
          //       <HomeGame />
          //     </div>
          //   )
          default:
            return null
        }
      })}
      {/* Game Sections */}
      <div className="mt-8 space-y-8">
        <GameSection
          title="Bingo Originals"
          games={bingoOriginalsGames}
          onViewAll={() => console.log('View All Bingo Originals')}
        />
        <GameSection title="Slots" games={slotsGames} onViewAll={() => console.log('View All Slots')} />
        <GameSection title="Jackpots" games={jackpotsGames} onViewAll={() => console.log('View All Jackpots')} />
      </div>

      <DescriptionCards />
      <div className="grid grid-cols-2 gap-6 md:grid-cols-3 lg:grid-cols-4">
        {gameGroup?.map((el, i) => <ProviderItem key={i} data={el} isDisabled={false} className="" />)}
      </div>
      {userData?.userInfo && (
        <>
          <HallPopup />
          <PromoTaskHallPopup />
          <DomainUrlModal />
          <ActivityShortcut />
        </>
      )}
    </div>
  )
}
