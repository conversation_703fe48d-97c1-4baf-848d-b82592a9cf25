import { NextResponse } from 'next/server'
import { ALL_LANGUAGES, getAllowedLanguages, LANGUAGE_COOKIE, languages } from '@/i18n/shared'

import acceptLanguage from 'accept-language'
import type { Middleware } from '@/middleware'
import { isPublicPath } from '@/utils/public-path'

export const i18nMiddleware: Middleware = async (request, response, next) => {
  const curlPathname = request.nextUrl.pathname

  if (isPublicPath(curlPathname)) {
    return next()
  }

  acceptLanguage.languages(languages.map((lang) => lang.value))

  let lng: string | null = null
  if (request.cookies.has(LANGUAGE_COOKIE)) {
    lng = acceptLanguage.get(request.cookies.get(LANGUAGE_COOKIE)!.value)
  }

  if (!lng) {
    // 從後端取得允許的語言
    try {
      const data = await getAllowedLanguages()
      if (data) {
        acceptLanguage.languages(data?.langTypeCds.split(','))
        lng = acceptLanguage.get(data.defaultLangType)
        const lang = ALL_LANGUAGES.find((loc) => curlPathname.startsWith(`/${loc}`))

        if (lang) {
          response.cookies.set(LANGUAGE_COOKIE, lang)
        }
      }
    } catch (error) {
      console.log('get business info error', error)
    }
  }

  const { pathname, search } = request.nextUrl

  const lang = ALL_LANGUAGES.find((loc) => pathname.startsWith(`/${loc}`))
  const isNext = pathname.startsWith('/_next')

  if (!Boolean(lang) && !isNext) {
    return NextResponse.redirect(new URL(`/${lng}${pathname}${search}`, request.url))
  }

  if (pathname) {
    const currentLng = request.cookies.get(LANGUAGE_COOKIE)?.value
    if (lang && lang !== currentLng) {
      response.cookies.set(LANGUAGE_COOKIE, lang)
    }
  }

  return next()
}
