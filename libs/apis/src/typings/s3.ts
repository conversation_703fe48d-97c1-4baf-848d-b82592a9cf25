export type AboutUs = {
  title: string
  html: string
  id: number
}

export type UserAgreement = {
  html: string
  icon: string
}

export type FooterType = {
  logo: string // 圖片
  copyright: string // 版權
  company: string // 公司
  issuer: string // 頒發機構
  contact: string // 聯絡資訊
  linkGroups: LinkGroup[] // 連結群組
  providers: Provider[] // 遊戲提供商
  license: License[] // 牌照資質
  licenseNumber: string
  socialMedias: SocialMedia[] // 社群媒體
}

export type License = {
  image: string // 圖片
  url: string // 連結
  className: string
  width: number
  height: number
}

export type LinkGroup = {
  groupName: string // 顯示在畫面上的名稱
  links: Link[]
}

export type Link = {
  name: string // 顯示在畫面上的名稱
  code: string // 後端編碼, 需要對應 LINK_CODE_MAP
  title?: string
  href?: string
  customName?: string
}

export type Provider = {
  name: string // 顯示在畫面上的名稱
  url: string // 連結
  image: string // 圖片
  darkImage: string // 圖片
  width: number
  height: number
  className: string
}

export type SocialMedia = {
  name: string // 顯示在畫面上的名稱
  url: string // 連結
  icon: string // 圖片
}

/*
 * Header
 */

export type HeaderType = HeaderItemType[]

export type ShowWhenType = 'always' | 'logged' | 'unlogged' // 显示条件: 总是显示、登录时显示、未登录时显示

export type HeaderItemType = {
  type: 'logo' | 'balance' | 'auth' // 头部项目类型: logo、余额、认证
  showWhen?: ShowWhenType // 显示条件: 总是显示、登录时显示、未登录时显示
  config?: HeaderConfigItem[] // 配置项数组
  image?: string // logo图片地址
}

export type HeaderConfigItem = {
  type: 'button' | 'userMenu' | 'link' | 'modal' | 'language' | 'login-register' | 'notice' | 'signIn' // 配置项类型: 按钮、用户菜单、链接、模态框、语言、签到
  name?: string // 显示文本
  showWhen?: ShowWhenType // 显示条件: 总是显示、登录时显示、未登录时显示
  buttonType?: 'default' | 'primary' // 按钮类型: 默认、主要
  href?: string // 链接地址
  action?: string // 动作类型
  menuItems?: UserMenuItem[] // 用户菜单配置项
}

export type UserMenuItem = {
  type: string // 菜单项类型,用于匹配图标
  name: string // 显示的多语言文本 key
  href?: string // 跳转链接
}

export type SliderBarType = {
  menus: SliderBarItems[]
}

export type SliderBarItem = {
  name?: string
  code?: string
  icon?: string;
  route?: string
  children?: SliderBarItem[]
  show?: boolean
  defaultShow?: boolean
  showArrow?: boolean;
  disabled?: boolean
  id?: number | string
  active?: boolean;
  expand?: boolean;
}


export type SliderBarItems = {
  title?: SliderBarItem
  menus?: SliderBarItem[]
}

export type SliderBarTopButtons = {
  name: string
  route: string
  disabled?: boolean
}

/**
 * Base
 * - 所有元件都要實作的介面
 */
export interface Base {
  title: string // 元件名稱，獨一無二的，與設計部是統一的
  style: Record<string, string> // 開放修改的接口 color | rounded | padding | margin 等設定，必須是 tailwind 的樣式實作
}

/**
 * GlobalConfig
 * - 全域設置
 */
export type GlobalConfig = Base & {
  walletMode: 'single' | 'multi' | 'asia-single' | 'betfury' // 錢包模式: 單幣錢包 | 多幣錢包
  businessId: string
  lottieShortcutVersion: 'v1' | 'v2' // 首頁動圖版本
  seo: {
    title: string
    description: string
    keywords: string
    image: string
    meta: string
  }
  brand: {
    // TODO: 品牌設置
    image: {
      logo: string // 登入頁顯示的
      loader: string // 載入中 gif
      favicon: string // 網頁 favicon
      darkLogo: string
    }
    notAllow: {
      image: string // 地區限制圖片
      html: string // 地區限制 html
      color: string // 地區限制顏色
    }
    // 目前暫時沒用到
    aboutUs: string // S3 的 aboutUs.json
    userAgreement: string // S3 的 userAgreement.json
    footer: string // S3 的 footer.json
    name: string // 品牌名稱
  }
  holiday?: {
    // TODO: 節日設置
  }
  pwa?: {
    name: string // 應用程式名稱
    short_name: string // 應用程式簡稱
    description: string // 應用程式描述
    background_color: string // 背景顏色
    theme_color: string // 主題顏色
    popup_description: string
    // 應用程式下載圖示尺寸 [48, 72, 96, 128, 256, 512]
    icons: {
      src: string // 應用程式下載圖示路徑
      sizes: string // 應用程式下載圖示尺寸
      type: string // 應用程式下載圖示類型
    }[]
  }
  agent: {
    isHidden: boolean // 是否隱藏代理人
  }
  // 警用
  vip: {
    isHidden: boolean // 是否隱藏 VIP
  }
  swap: 'open' | 'close' // 是否開啟 swap 闪兑
  custodial: 'open' | 'close' // 是否開啟 custodial 數位資產 托管钱包
  miniswap: 'open' | 'close' // 是否開啟 mini swap
  cookiePolicy: 'open' | 'close' // 是否開啟 cookie policy
  serviceUnavailable: {
    // 維護頁面的設置
    logo: string // 維護中圖片
    title: string // 維護中標題
    description: string // 維護中描述
    customerLink: string // 客服連結
  }
}

export type S3DbJson = {
  config: GlobalConfig // 全域設置
  page: {
    home: Base & {
      components: Base[]
    }
    profile: Base & {
      components: Base[]
    }
  }
}

export type CookiePolicy = {
  html: string
}
