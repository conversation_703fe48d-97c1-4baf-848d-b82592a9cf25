import {
  createApi,
  FooterType,
  HeaderType,
  S3Db<PERSON>son,
  UserAgreement,
  CookiePolicy,
  VIP_OPEN_STATUS,
  VipLevelData,
} from '@libs/apis/src'
import { create } from 'zustand'
// import { persist, createJSONStorage } from 'zustand/middleware'

export type HiddeModule = {
  vip: boolean
  agent: boolean
}

type State = {
  layoutData: {
    dbJson: S3DbJson | null
    footerJson: FooterType | null
    headerJson: HeaderType | null
    userAgreement: UserAgreement | null
    cookiePolicy: CookiePolicy | null
  } | null
  h5Url: string
  vipConf?: VipLevelData
  agentConf?: { reason: string; inBlacklist: boolean }
  isPlaying: boolean
}

type Action = {
  hasCrypto: () => boolean
  setLayoutData: (layoutData: State['layoutData']) => void
  reset: () => void
  initVipAndAgent: () => Promise<void>
  /** true 为隐藏 */
  getHiddenModule: () => HiddeModule
  setH5Url: (h5Url?: string) => void
  setIsPlaying: (isPlaying: boolean) => void
}

const initialState: State = {
  layoutData: null,
  h5Url: '',
  vipConf: undefined,
  agentConf: undefined,
  isPlaying: false,
}

export const useLayoutStore = create<State & Action>((set, get) => ({
  ...initialState,
  hasCrypto: () => {
    return get().layoutData?.dbJson?.config?.custodial === 'open'
  },
  setLayoutData: (layoutData) =>
    set(() => ({
      layoutData,
    })),
  reset: () => {
    set(initialState)
  },
  initVipAndAgent: async () => {
    const { data: agentConf } = await createApi().apis.agent.checkUserInBlacklist({
      data: null,
    })

    const { data: vipConf } = await createApi().apis.activity.getMemberVipLevel({
      data: {},
    })

    set(() => ({
      vipConf,
      agentConf,
    }))
  },
  getHiddenModule: () => {
    return {
      vip: get().vipConf?.statusCd === VIP_OPEN_STATUS.DISABLE,
      agent: Boolean(get().agentConf?.inBlacklist),
    }
  },
  setH5Url: (h5Url?: string) => {
    set(() => ({
      h5Url,
    }))
  },
  setIsPlaying: (isPlaying: boolean) => {
    set(() => ({
      isPlaying,
    }))
  },
}))
