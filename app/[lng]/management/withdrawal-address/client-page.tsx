'use client'

import ContentHeaderBar from '@/components/client/content-header-bar'
import { <PERSON>ert, Button, Empty, Space, Table, TableProps, ConfigProvider } from 'antd'
import { useTranslate } from '@tolgee/react'
import { useState } from 'react'
import AddWithdrawalAddress from './components/add-withdrawal-address'
import DeleteWithdrawalAddress from './components/delete-withdrawal-address'
import { type Address, createApi } from '@libs/apis'
import useSWRMutation from 'swr/mutation'
import { useMount } from 'ahooks'
import EmptyContent from '@/components/client/empty-content'
import { useSecondaryVerificationModalStore } from '@/components/client/secodary-verification/store'
import NoWithdrawConfirm from '@/components/client/secodary-verification/no-withdraw-confirm'

export default function WithdrawalAddressClientPage() {
  const { t } = useTranslate()

  const { getVerifyBaseInfo } = useSecondaryVerificationModalStore()

  const [editItem, setEditItem] = useState<Address | undefined>(undefined)
  const [openWithdrawalAddressModal, setOpenWithdrawalAddressModal] = useState(false)

  const [deleteItem, setDeleteItem] = useState<Address | undefined>(undefined)
  const [openDeleteWithdrawalAddressModal, setOpenDeleteWithdrawalAddressModal] = useState(false)
  const [openNoWithdrawConfirmModal, setOpenNoWithdrawConfirmModal] = useState(false)

  const { trigger: getVerifyBaseInfoFunc } = useSWRMutation('getVerifyBaseInfo', getVerifyBaseInfo)
  const { data: withdrawalAddressList, trigger: getWithdrawalAddressList } = useSWRMutation(
    `WithdrawalAddressClientPage`,
    async () => {
      const res = await createApi().apis.finance.getWithdrawAddress()
      return res.data?.addressList || []
    },
  )

  const handleOpenAddPayoutAccountModalFunc = async (item?: Address) => {
    setEditItem(item)

    const { isNoWithdrawVerify } = await getVerifyBaseInfoFunc()
    if (isNoWithdrawVerify) {
      setOpenNoWithdrawConfirmModal(true)
      return
    }

    setOpenWithdrawalAddressModal(true)
  }

  useMount(() => {
    getWithdrawalAddressList()
  })

  return (
    <div className="min-h-[50vh] px-8">
      <div className="mx-auto max-w-[1200px]">
        <ContentHeaderBar title={t('withdrawal_address.title', {})}>
          <Button
            type="primary"
            size="large"
            onClick={() => {
              handleOpenAddPayoutAccountModalFunc()
            }}
          >
            {t('withdrawal_address.add_withdrawal_address', {})}
          </Button>
        </ContentHeaderBar>

        <Alert
          className="my-4"
          type="info"
          showIcon
          message={<span className="text-sm text-primary-1">{t('withdrawal_address.alert', {})}</span>}
        />

        {!withdrawalAddressList?.length && (
          <EmptyPayoutMethod
            onAdd={() => {
              handleOpenAddPayoutAccountModalFunc()
            }}
          />
        )}

        {withdrawalAddressList && Boolean(withdrawalAddressList?.length) && (
          <WithdrawalAddressList
            list={withdrawalAddressList}
            onEdit={(item) => {
              handleOpenAddPayoutAccountModalFunc(item)
            }}
            onDelete={(item) => {
              setDeleteItem(item)
              setOpenDeleteWithdrawalAddressModal(true)
              getWithdrawalAddressList()
            }}
          />
        )}
      </div>

      {openWithdrawalAddressModal && (
        <AddWithdrawalAddress
          open={openWithdrawalAddressModal}
          editItem={editItem}
          onCancel={() => setOpenWithdrawalAddressModal(false)}
          onOk={() => {
            setOpenWithdrawalAddressModal(false)
            getWithdrawalAddressList()
          }}
        />
      )}

      {openDeleteWithdrawalAddressModal && (
        <DeleteWithdrawalAddress
          open={openDeleteWithdrawalAddressModal}
          deleteItem={deleteItem}
          onCancel={() => setOpenDeleteWithdrawalAddressModal(false)}
          onSubmit={() => {
            setDeleteItem(undefined)
            getWithdrawalAddressList()
          }}
        />
      )}

      <NoWithdrawConfirm open={openNoWithdrawConfirmModal} onCancel={() => setOpenNoWithdrawConfirmModal(false)} />
    </div>
  )
}

// 空状态
function EmptyPayoutMethod({ onAdd }: { onAdd: () => void }) {
  const { t } = useTranslate()
  return (
    <div className="flex min-h-[500px] flex-col items-center justify-center">
      <Empty className="mb-4" description={t('withdrawal_address.empty_withdrawal_address', {})} />
      <Button variant="outlined" color="primary" size="large" onClick={onAdd}>
        + {t('withdrawal_address.add_withdrawal_address', {})}
      </Button>
    </div>
  )
}

// 列表
function WithdrawalAddressList({
  list,
  onEdit,
  onDelete,
}: {
  list: Address[]
  onEdit: (item: Address) => void
  onDelete: (item: Address) => void
}) {
  const { t } = useTranslate()
  const columns: TableProps<Address>['columns'] = [
    {
      title: t('withdrawal_address.withdrawal_address', {}),
      dataIndex: 'address',
      width: '50%',
      render: (_, item) => {
        return <div className="text-shades-100">{item.address}</div>
      },
    },
    {
      title: t('withdrawal_address.remark', {}),
      dataIndex: 'remark',
    },
    {
      title: t('withdrawal_address.action', {}),
      dataIndex: 'action',
      align: 'center',
      width: '150px',
      render: (_, item) => {
        return (
          <Space size="small" className="flex items-center justify-center gap-2">
            <div className="cursor-pointer text-sm text-primary-1" onClick={() => onEdit(item)}>
              {t('withdrawal_address.edit', {})}
            </div>
            <div className="cursor-pointer text-sm text-error-2" onClick={() => onDelete(item)}>
              {t('withdrawal_address.delete', {})}
            </div>
          </Space>
        )
      },
    },
  ]
  return (
    <ConfigProvider
      theme={{
        components: {
          Table: {
            colorBgBase: 'var(--color-gs-200)',
            colorText: 'var(--color-bgs-900)',
            headerBorderRadius: 10,
          },
        },
      }}
    >
      <Table
        columns={columns}
        dataSource={list}
        rowKey={(item) => item.id}
        pagination={false}
        locale={{
          emptyText: <EmptyContent />,
        }}
      />
    </ConfigProvider>
  )
}
