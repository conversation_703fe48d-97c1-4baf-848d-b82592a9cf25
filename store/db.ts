import { openThirdPartyUrl } from '@/utils/open-page'
import type { S3DbJson } from '@libs/apis'
import type { CustomerConfig } from '@libs/typings'
import { create } from 'zustand'

type DbStore = {
  db: S3DbJson | null
  setDb: (db: S3DbJson) => void
  customerConfig: CustomerConfig
  setCustomerConfig: (customerConfig: DbStore['customerConfig']) => void
  openCustomerService: () => void
  checkIsSingleWallet: () => boolean
}

export const useDbStore = create<DbStore>((set, get) => ({
  db: null,
  setDb: (db: S3DbJson) => set({ db }),
  checkIsSingleWallet: () => {
    return get().db?.config?.walletMode === 'single'
  },
  customerConfig: {},
  setCustomerConfig: (customerConfig: DbStore['customerConfig']) => set({ customerConfig }),
  openCustomerService: () => {
    const url = get().customerConfig.customer_service_url
    if (url) {
      openThirdPartyUrl({ type: 'link', data: url })
    }
  },
}))
