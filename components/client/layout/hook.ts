import { useCallback, useEffect, useMemo, useState } from 'react'

// Tolgee imports
import { useTolgee, useTranslate } from '@tolgee/react'

import enUS from 'antd/locale/en_US'
import zhCN from 'antd/locale/zh_CN'
import zhHK from 'antd/locale/zh_HK' //

// Hook imports
import { useFooter } from './footer/hooks/useFooter'
import { useAuth } from '@modules/auth'
import { useSearchParams } from 'next/navigation'
import { useUserStore } from '@/store/user'
import { useDbStore } from '@/store/db'

import { TabsTypeLogRegisterType } from '@/enums/login'
import { ROUTES_PARAMS_KEY, ROUTES_PARAMS_VALUE } from '@/constants/routes'
import { setToastTexts } from '@/components/client/toast'

// Style imports
import '@/styles/layout/index.css'
import { antdConfig } from '@/styles/config-provider'
import {
  createTaskSchedule,
  getWindowStorageByKey,
  setWindowStorageByKey,
  transformTime2millisecond,
} from '@libs/utils/src'
import { ThemeEnum } from '@libs/typings'
import { S3DbJson, UserAgreement, FooterType, HeaderType, CookiePolicy, createApi } from '@libs/apis'
import { usePathname } from 'next/navigation'

import type { CustomerConfig } from '@libs/typings'

export type Props = {
  dbJson: S3DbJson | null
  footerJson: FooterType | null
  headerJson: HeaderType | null
  userAgreement: UserAgreement | null
  cookiePolicy: CookiePolicy | null
  children: React.ReactNode
  h5Url?: string
  customerConfig: CustomerConfig
}

const fullContentPathList = ['/support/about-us']

export const useLayout = (props: Props) => {
  const { dbJson, customerConfig } = props
  const search = useSearchParams()
  const pathname = usePathname()
  const { t } = useTranslate()
  const { isLogin } = useAuth()
  const { setDb, setCustomerConfig } = useDbStore()
  const isFullContentPage = useMemo(() => fullContentPathList.some((e) => pathname.includes(e)), [pathname])
  const { setLogRegisterVisible, setRegisterLoginModalActiveKey } = useUserStore()
  const { getLanguage } = useTolgee()

  const [isDark, setIsDark] = useState<boolean>(getWindowStorageByKey('isDark') ?? true)

  const showFooter = useFooter()

  const { theme, ...rest } = antdConfig || {}

  const handleChangeTheme = useCallback((isDark: boolean) => {
    setIsDark(isDark)
    setWindowStorageByKey('isDark', isDark)

    // 添加主题类名
    document.body.classList.remove(ThemeEnum.dark, ThemeEnum.light)
    document.body.classList.add(isDark ? ThemeEnum.dark : ThemeEnum.light)
  }, [])

  const locale = useMemo(() => {
    const language = getLanguage()
    if (language === 'zh-CN') return zhCN
    if (language === 'zh-HK') return zhHK
    return enUS
  }, [getLanguage])

  useEffect(() => {
    const sign = search.get(ROUTES_PARAMS_KEY.SIGN)
    const needLogin = sign === ROUTES_PARAMS_VALUE.NEED_LOGIN
    const needReg = sign === ROUTES_PARAMS_VALUE.NEED_REGISTER

    if (isLogin) {
      return
    }

    if (needLogin) {
      setLogRegisterVisible(true)
      setRegisterLoginModalActiveKey(TabsTypeLogRegisterType.login)
    }
    if (needReg) {
      setLogRegisterVisible(true)
      setRegisterLoginModalActiveKey(TabsTypeLogRegisterType.register)
    }
  }, [search, isLogin])

  useEffect(() => {
    if (dbJson) {
      setDb(dbJson)
    }
    if (customerConfig) {
      setCustomerConfig(customerConfig)
    }
  }, [dbJson, customerConfig])

  useEffect(() => {
    setIsDark(isDark)
    handleChangeTheme(isDark)
  }, [isDark])

  useEffect(() => {
    const task = createTaskSchedule({
      id: 'refresh-token',
      immediateRun: true,
      func: async () => {
        if (isLogin) {
          await createApi().apis.auth.checkSession()
        }
      },
      delay: transformTime2millisecond(5, 'minutes'),
    })

    task.run()

    return () => {
      task.cancel()
    }
  }, [isLogin])

  useEffect(() => {
    setToastTexts({
      title: t('common.reminder', {}),
      confirmButton: t('common.confirm', {}),
      successTitle: t('common.reminder', {}), //his
      errorTitle: t('common.reminder', {}), //his
    })
  }, [t])

  return {
    locale,
    showFooter,
    theme,
    rest,
    handleChangeTheme,
    setToastTexts,
    t,
    isLogin,
    search,
    isDark,
    setDb,
    setIsDark,
    dbJson,
    isFullContentPage,
  }
}
