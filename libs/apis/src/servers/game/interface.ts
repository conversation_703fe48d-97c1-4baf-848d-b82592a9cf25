import {
  ApiResponse,
  SidebarGameListQueryParams,
  SidebarGameListResponse,
  GameRecordsQueryParams,
  GameRecordsResponse,
  GroupQueryParams,
  GroupItem,
  GameListQueryParams,
  GameListResponse,
  GetRecentGameParams,
  PageSearchRes,
  SidebarGameItem,
  PostCollectGameParams,
  GameDetailParams,
  GameDetailResponse,
  RecommendGameReq,
  HotGameSearchReq,
  GameGroupResponse,
  GameLinkQueryParams,
  GameLink,
  TabGameListQueryParams,
  SuccessResponseV3,
} from '../../interface'

export type GamePath = keyof GameApi

export type GameApi = {
  /**
   * 側邊欄底下的「場館」和「遊戲」
   * [GET] /xc-game/v1/ra-hall/sidebar/game/list
   * https://yapi.nbttfc365.com/project/83/interface/api/22045
   * - query params
   *   - sidebarId
   *     - https://yapi.nbttfc365.com/project/83/interface/api/24368 的 id
   * - response
   *   - "direct": "no"
   *     - 可以直接進入遊戲裡面
   *   - "direct": "no" && "isGroup": true
   *     - 用「groupId」帶入 https://yapi.nbttfc365.com/project/83/interface/api/19024 「groupId」查詢場館下面的遊戲
   */
  getSidebarGameList: ApiResponse<SidebarGameListQueryParams, SidebarGameListResponse>

  /**
   * 遊戲紀錄
   * [GET] /xc-game/v1/ra-hall/gameRecord
   * https://yapi.nbttfc365.com/project/83/interface/api/19079
   */
  getGameRecords: ApiResponse<GameRecordsQueryParams, GameRecordsResponse>

  /**
   * 未結算遊戲紀錄
   * [GET] /xc-game/v1/ra-hall/gameRecord/unsettled
   * https://yapi.nbttfc365.com/project/83/interface/api/24405
   */
  getUnsettledGameRecords: ApiResponse<GameRecordsQueryParams, GameRecordsResponse>

  /**
   * 遊戲館列表
   * [GET] /xc-game/v1/ra-hall/group/list
   * https://yapi.nbttfc365.com/project/83/interface/api/19029
   * - query params
   *   - walletType : single_wallet
   *     - 只讀取這個範圍
   *   - groupTypeCd : ""
   *     - 空值表示全部
   * - response
   *   - "id": 360234
   *     - 帶入 https://yapi.nbttfc365.com/project/83/interface/api/19024 「groupId」查詢場館下面的遊戲
   */
  getGroupList: ApiResponse<GroupQueryParams, GroupItem[]>

  /**
   * 遊戲列表
   * [GET] /xc-game/v1/ra-hall/game/list
   * https://yapi.nbttfc365.com/project/83/interface/api/19024
   * - query params
   *   - groupId
   *     - https://yapi.nbttfc365.com/project/83/interface/api/19029 的 「id」"id": 360308
   *   - pageNum
   *   - pageSize
   */
  getGameList: ApiResponse<GameListQueryParams, GameListResponse>

  /**
   * 遊戲列表
   * [GET] /xc-game/v1/ra-hall/tab/game/list
   * https://xc-gateway-staging.jklkjnqscc.com/doc.html#/xc-game/tab-controller/tabGameList
   * - query params
   *   - groupId
   *     - https://yapi.nbttfc365.com/project/83/interface/api/19029 的 「id」"id": 360308
   *   - pageNum
   *   - pageSize
   */
  getTabGameList: ApiResponse<TabGameListQueryParams, GameListResponse>

  /**
   * 最近的遊戲
   * [GET] /xc-game/v1/ra-hall/game/recent
   * https://yapi.nbttfc365.com/project/83/interface/api/19044
   */
  getRecentGames: ApiResponse<GetRecentGameParams, PageSearchRes<SidebarGameItem>>

  /**
   * 查詢收藏的遊戲
   * [GET] /xc-game/v1/ra-hall/game/collect
   * https://yapi.nbttfc365.com/project/83/interface/api/19049
   */
  getCollectGames: ApiResponse<{ pageNum: number; pageSize: number }, PageSearchRes<SidebarGameItem>>

  /**
   * 增減收藏遊戲
   * [POST] /xc-game/v1/ra-hall/game/collect
   * https://yapi.nbttfc365.com/project/83/interface/api/19384
   */
  postCollectGame: ApiResponse<PostCollectGameParams, SuccessResponseV3>

  /**
   * 遊戲詳情
   * [GET] /xc-game/v1/ra-hall/game/detail
   * https://yapi.nbttfc365.com/project/83/interface/api/20292
   * - 如果是小遊戲（電子遊戲）：參數要帶入 gameId
   * - 如果是大遊戲（真人視訊｜體育遊戲）：參數要帶入 groupId
   */
  getGameDetail: ApiResponse<GameDetailParams, GameDetailResponse>

  /**
   * 推薦的遊戲 (綜合)
   * [GET] /xc-game/v1/ra-hall/game/recommend
   * https://yapi.nbttfc365.com/project/83/interface/api/24404
   */
  getRecommendGames: ApiResponse<RecommendGameReq, PageSearchRes<SidebarGameItem>>

  /**
   * 熱門的遊戲
   * [GET] /xc-game/v1/ra-hall/game/hot
   * https://yapi.nbttfc365.com/project/83/interface/api/19039
   */
  getHotGames: ApiResponse<HotGameSearchReq, PageSearchRes<SidebarGameItem>>

  /**
   * 提供商查詢
   * [GET] /xc-game/v1/ra-hall/game-group-supplier
   * https://yapi.nbttfc365.com/project/83/interface/api/20161
   */
  getGameGroup: ApiResponse<{}, GameGroupResponse>

  /**
   * 進入遊戲V2 (取得遊戲連結)
   * [GET] /xc-game/v1/ra-hall/game/startV2
   * https://yapi.nbttfc365.com/project/83/interface/api/22033
   * - 如果是小遊戲（電子遊戲），「gameId」和「groupId」都要
   * - 如果是大遊戲（真人視訊｜體育遊戲），放「groupId」 ** 大遊戲沒有 id **
   * - 「coinName」：CNY | USD | ...
   */
  getGameLink: ApiResponse<GameLinkQueryParams, GameLink>

  /**
   * 試玩遊戲 (取得遊戲連結)
   * [GET] /xc-game/v1/ra-hall/game/startAnonymous
   * https://yapi.nbttfc365.com/project/83/interface/api/23984
   * - 如果是小遊戲（電子遊戲），「gameId」和「groupId」都要
   * - 如果是大遊戲（真人視訊｜體育遊戲），放「groupId」 ** 大遊戲沒有 id **
   * - 「coinName」：CNY | USD | ...
   */
  getAnonymousGameLink: ApiResponse<GameLinkQueryParams, GameLink>
}
