import { cn } from '@libs/utils/src'
import { motion } from 'motion/react'
import Image, { StaticImageData } from 'next/image'
import { useMemo, useState } from 'react'

// components
import { BaseCard } from '../cards'
import Wheel from './wheel'

// images
import RuleIcon from '@/components/client/lucky-wheel/images/rules.svg'
import btnRotateCn from '@/components/client/lucky-wheel/images/neon-b/btn_rotate_cn.png'
import btnRotateEn from '@/components/client/lucky-wheel/images/neon-b/btn_rotate_en.png'

import icon1 from '@/components/client/lucky-wheel/images/neon-b/icon_1.png'
import icon2 from '@/components/client/lucky-wheel/images/neon-b/icon_2.png'
import icon3 from '@/components/client/lucky-wheel/images/neon-b/icon_3.png'
import icon4 from '@/components/client/lucky-wheel/images/neon-b/icon_4.png'
import icon5 from '@/components/client/lucky-wheel/images/neon-b/icon_5.png'
import icon6 from '@/components/client/lucky-wheel/images/neon-b/icon_6.png'
import icon7 from '@/components/client/lucky-wheel/images/neon-b/icon_7.png'
import icon8 from '@/components/client/lucky-wheel/images/neon-b/icon_8.png'
import icon9 from '@/components/client/lucky-wheel/images/neon-b/icon_9.png'
import icon10 from '@/components/client/lucky-wheel/images/neon-b/icon_10.png'

// utils
import { padNumberString } from '@/components/client/lucky-wheel/utils'
import { LottieS3 } from '../../../lottie-s3'
import { sortBy } from 'lodash-es'
import { useStarWheel } from '../../../hooks'

export interface LuckyWheelProps {
  currencyUnit?: string
  width?: number
  lang: 'en' | 'zh'
  prizeList: Array<{
    icon?: StaticImageData
    label: string
    isHighlight?: boolean
  }>
  wheelCount?: number
  className?: string
  onEnd?: (res: string) => void
  onShowRules?: (show: boolean) => void
}

export default function LuckyWheelNeonB(
  props: LuckyWheelProps & {
    onClose?: () => void
    handleStart?: () => Promise<number | undefined>
  },
) {
  const { width = 345, lang = 'en' } = props
  const [isSpinning, setIsSpinning] = useState(false)
  const icons = [icon1, icon2, icon3, icon4, icon5, icon6, icon7, icon8, icon9, icon10]

  const { wheelRef, handleStart } = useStarWheel({
    isSpinning,
    setIsSpinning,
    handleStart: props?.handleStart,
  })

  const prizeList = useMemo(() => {
    return props.prizeList.map((item, index) => {
      return {
        ...item,
        icon: icons[index],
      }
    })
  }, [icons, props.prizeList])

  return (
    <div className="flex items-center">
      <BaseCard width={width} className={cn('pt-[25%]')} buttonsDivClassName={cn('top-[13%]')} onClose={props?.onClose}>
        <div className="flex h-[16%] items-end pb-[1%]">
          <LottieS3
            lottieSource={
              lang === 'en'
                ? 'https://tw-s3-image.s3.ap-southeast-1.amazonaws.com/lottie/luckwheel-2-logo_en.json'
                : 'https://tw-s3-image.s3.ap-southeast-1.amazonaws.com/lottie/luckwheel-2-logo_cn.json'
            }
            className={cn('scale-[2]')}
          />
        </div>

        {/* rules */}
        <RuleIcon
          className={cn('absolute left-0 top-0 h-[34px] w-[34px] cursor-pointer rounded-xl border p-1')}
          onClick={() => props?.onShowRules?.(true)}
        />

        <div
          className={cn('m-auto flex h-[9%] w-[80%] items-center gap-2', {
            'mt-[4%]': lang === 'zh',
            'mt-[5%]': lang === 'en',
          })}
        >
          {sortBy(prizeList, (item) => -item.label)
            .slice(0, 3)
            .map((item, index) => (
              <div
                key={index}
                className="flex flex-1 flex-col items-center rounded-lg bg-gradient-to-b from-[#2f1632] to-[#58335f] leading-4"
              >
                <Image src={item.icon} width={30} height={30} alt="地主" />
                <motion.div
                  initial={{ opacity: 0, y: -30 }}
                  animate={{
                    opacity: isSpinning ? 0 : 1,
                    y: isSpinning ? 30 : 0,
                    height: isSpinning ? 0 : 'auto',
                    marginTop: isSpinning ? 0 : '-5%',
                  }}
                  exit={{ opacity: 0, y: 30 }}
                  transition={{ duration: 0.3 }}
                  className={cn('text-center text-3xs text-[12px] font-semibold text-shades-100')}
                >
                  <span className="text-[#FFEBCE]">
                    {padNumberString(item.label)} {props?.currencyUnit}
                  </span>
                </motion.div>
              </div>
            ))}
        </div>

        <motion.div
          className="mb-[2%]"
          animate={{
            scale: isSpinning ? 1.1 : 1,
            transition: {
              duration: 0.3,
            },
          }}
        >
          <Wheel
            ref={wheelRef}
            className="m-auto"
            prizeList={prizeList}
            lang={props.lang}
            width={width * 0.75}
            onEnd={(res) => {
              setIsSpinning(false)
              props?.onEnd?.(res)
            }}
            handleStart={handleStart}
          />
        </motion.div>

        <motion.div
          animate={{
            opacity: isSpinning ? 0 : 1,
            y: isSpinning ? 30 : 0,
            height: isSpinning ? 0 : 'auto',
          }}
        >
          <Image
            src={lang === 'en' ? btnRotateEn : btnRotateCn}
            width={200}
            height={50}
            className="z-2 relative m-auto h-[auto] w-[50%]"
            alt="action"
            onClick={handleStart}
          />

          <div className="text-center text-sm">
            {
              //剩余Spin次数:{props?.wheelCount} 次
              lang === 'zh'
                ? `剩余Spin次数: ${props?.wheelCount} 次`
                : `Remaining spin times: ${props?.wheelCount} times`
            }
          </div>

          <div className="m-auto flex gap-2 px-[36px] pt-1">
            {sortBy(prizeList, (item) => Number(item.label))
              .slice(0, 3)
              .map((item, index) => (
                <div key={index} className="flex flex-1 items-center rounded-lg">
                  <Image src={item.icon} width={18} height={18} alt="地主" />
                  <div className={cn('pl-2 text-center text-3xs text-[12px] font-semibold leading-4 text-shades-100')}>
                    {padNumberString(item.label)}
                  </div>
                </div>
              ))}
          </div>
        </motion.div>
      </BaseCard>
    </div>
  )
}
