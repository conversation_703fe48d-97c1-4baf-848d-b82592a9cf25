import { Link, LinkGroup, SocialMedia } from '@libs/apis/src'
import FooterLinkGroupItem from './footer-link-group-item'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
type Props = {
  linkGroups: LinkGroup[]
  socialMedias: SocialMedia[]
  groupLinkClick: (item: Link) => void
}

export default function FooterLinkGroup({ linkGroups, socialMedias, groupLinkClick }: Props) {
  const router = useRouter()
  if (!linkGroups?.length) return null
  return (
    <div className="footer-middle flex flex-col gap-6 xl:flex-row">
      {linkGroups.map((linkGroup) => {
        return <FooterLinkGroupItem key={linkGroup.groupName} linkGroup={linkGroup} groupLinkClick={groupLinkClick} />
      })}
      {socialMedias.length && (
        <div className="min-w-[130px] space-y-4">
          <div className="text-sm text-[#364249]">Community</div>
          <div className="flex flex-row flex-wrap gap-4 xl:flex-col">
            {socialMedias.map((item) => {
              return (
                <div
                  onClick={() => router.push(item.url)}
                  key={item.name}
                  className="inline-flex cursor-pointer items-center space-x-2 text-sm font-medium text-[#8B9BA7] hover:text-white"
                >
                  {item?.icon && (
                    <Image
                      src={item.icon}
                      alt={item.name}
                      width={22}
                      height={22}
                      className="h-[22px] w-[22px] object-contain"
                    />
                  )}
                  <span>{item.name}</span>
                </div>
              )
            })}
          </div>
        </div>
      )}
    </div>
  )
}
