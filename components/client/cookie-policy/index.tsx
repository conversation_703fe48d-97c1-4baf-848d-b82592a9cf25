import { cn } from '@libs/utils/src'
import { useState, useEffect, useRef } from 'react'
import { Button } from 'antd'
import { useTranslate } from '@tolgee/react'
import { jscookie } from '@/libs/utils'
import Link from 'next/link'

export default function CookiePolicy() {
  const { t } = useTranslate()
  const [isVisible, setIsVisible] = useState(true)
  const [position, setPosition] = useState(0)
  const policyRef = useRef<HTMLDivElement>(null)

  // 检查cookie是否已接受
  const cookieAccepted = jscookie.get('cookie_accepted') === 'true'

  // 监听滚动和窗口大小变化事件
  useEffect(() => {
    const appContent = document.getElementById('app-content')
    if (!appContent) return

    const visibleTop = window.innerHeight - 150

    // 初始设置位置
    setPosition(visibleTop)

    // 滚动事件处理函数
    const handleScroll = (event: Event) => {
      const target = event.target as HTMLElement
      setPosition(target.scrollTop + visibleTop)
    }

    // 窗口大小变化处理函数
    const handleResize = () => {
      const newVisibleTop = window.innerHeight - 150
      setPosition(appContent.scrollTop + newVisibleTop)
    }

    // 添加事件监听
    appContent.addEventListener('scroll', handleScroll)
    window.addEventListener('resize', handleResize)

    // 清理事件监听
    return () => {
      appContent.removeEventListener('scroll', handleScroll)
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  if (cookieAccepted || !isVisible) {
    return null
  }

  const handleAccept = () => {
    jscookie.set('cookie_accepted', 'true', { expires: 365 }) // 保存一年
    setIsVisible(false)
  }

  const handleReject = () => {
    // 可以在这里处理拒绝逻辑，这里简单实现为隐藏提示
    setIsVisible(false)
  }

  return (
    <div
      ref={policyRef}
      className={cn(
        'absolute left-1/2 z-30 flex w-[80%] max-w-[1000px] -translate-x-1/2 items-center justify-between rounded-[12px] bg-shades-100 px-6 py-[10px] shadow-lg',
      )}
      style={{ top: `${position}px` }} // 动态设置top位置
    >
      <div className="mr-4 flex-1 text-xs text-gs-100">
        {t(
          'cookie.message',
          'This website uses cookies to ensure you get the best experience. By continuing to browse, you agree to our',
        )}
        <Link href={`/support/cookie-policy`} className="mx-1 text-primary-1 hover:underline">
          {t('cookie.link', 'Cookies Policy')}
        </Link>
      </div>
      <div className="flex items-center space-x-2">
        <Button
          className="!h-[36px] min-w-[80px] rounded-lg bg-primary-1 text-shades-100"
          type="primary"
          size="large"
          onClick={handleAccept}
        >
          {t('cookie.accept', 'Accept')}
        </Button>
        <Button
          className="!h-[36px] min-w-[80px] rounded-lg !border-gs-500 !bg-gs-500 text-shades-100"
          type="primary"
          size="large"
          onClick={handleReject}
        >
          {t('cookie.reject', 'Reject')}
        </Button>
      </div>
    </div>
  )
}
