import { codeToRouteMapping } from '@/constants/routes'
import { HiddeModule } from '@/store/layout'
import { useSilderBarStore } from '@/store/silder-bar'
import { useUserStore } from '@/store/user'
import { AboutUs, HallTabItem, Link, LinkGroup } from '@libs/apis/src'
import { useRouter } from 'next/navigation'

export const useFooter = () => {
  return true
}

export function createLinkUrl(code: string) {
  return codeToRouteMapping[code] || ''
}

export const createGameUrl = (code: string, gameList: HallTabItem[]): string => {
  const gameListCode = gameList.map((item) => ({
    ...item,
    href: `/mini-game/game-shows?tabId=${item.id}&code=${item.code}`,
  }))
  return gameListCode?.find((game) => game.code.includes(code))?.href || ''
}

export const useFooterPath = () => {
  const router = useRouter()

  const { gameList } = useSilderBarStore()
  const { setNotifyModalOpen } = useUserStore()
  const groupLinkClick = (item: Link) => {
    if (item.code === 'online_support') {
      setNotifyModalOpen(true)
      return
    }

    router.push(item?.href || createGameUrl(item.code, gameList) || createLinkUrl(item.code))
  }

  return {
    gameList,
    groupLinkClick,
  }
}

export const userAgreementAgeImage = (icon: string) => {
  return icon ? `/svg/user-agreement/${icon}.svg` : null
}

export const concatAboutUs = (
  aboutUs: AboutUs[],
  linkGroup: LinkGroup[],
  hiddeModule: HiddeModule,
  gameList: HallTabItem[],
  vipStatus: boolean,
  isLogin: boolean,
) => {
  const newLinkGroup = linkGroup
    ?.map((group) => ({
      ...group,
      links: group.links.filter((link) => {
        if (!vipStatus && link.code === 'vip' && isLogin) return false
        if (hiddeModule.agent && link.code === 'agent') return false
        return true
      }),
    }))
    .map((group) => {
      if (group.groupName === 'Games') {
        return {
          ...group,
          links: group.links.map((link) => ({
            ...link,
            customName: gameList.find((list) => list.code === link.code)?.name || link.name,
          })),
        }
      }
      return group
    })

  if (!aboutUs.length) return newLinkGroup
  const aboutUsList = [
    {
      code: `about_us`,
      name: 'about_us',
      title: 'about_us',
      href: `/support/about-us`,
      customName: 'about_us',
    },
  ]
  return newLinkGroup?.map((group) => ({
    ...group,
    links: group.links.flatMap((link) => (link.code === 'about_us' ? aboutUsList : link)),
  }))
}
