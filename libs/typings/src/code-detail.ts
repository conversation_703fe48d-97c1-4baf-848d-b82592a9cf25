// 查詢字典檔用
export enum CodeDetail {
  // 金字塔返佣列表
  AgentProductCd = 'agent_product_cd',
  // 返佣类型
  RebateTypeCd = 'rebate_type_cd',
  // 密保问题
  Confidentiality = 'Confidentiality',
  // 支援銀行
  SupportedBank = 'banco_de_pagamento',
  // 支援支付方式
  SupportedPayment = 'c2c_payment_support_items',
  // 账户支付方式
  AccountPaymentType = 'c2c_payment_type_cd',

  // 法幣訂單類型
  FiatOrderType = 'fiatOrderType',

  // 法幣訂單狀態
  FiatOrderStatus = 'fiatOrderStatus',

  // 遊戲類型
  GameTypes = 'game_and_group_type',
  /** 产品线 */
  // agentProductCd = 'agent_product_cd',
  /** 产品线 - 显示返佣/手续费描述 */
  // agentProductCdRatio = 'agent_product_cd_ratio',
  agentProductCdRatio = 'ra_agent_product_cd_ratio',
  /** 产品线 - 仅展示产品名 */
  // agentProductCdShowRatio = 'agent_product_cd_show_ratio',
  agentProductCdShowRatio = 'ra_agent_product_cd_show_ratio',
  /** 代理模式/代理类型 */
  agentTypeCode = 'agent_type_code',
  /** 代理商审核状态 */
  approvalStatusInd = 'approvalStatusInd',
  /** 代理商等级规则 */
  // agentGradeRules = 'agt_grade_rules_cd',
  /** 三级代理等级规则 */
  agentThreeGradeRules = 'agt_grade_three_level_rules_cd',
  /** 区域代理等级规则 */
  agentAreaGradeRules = 'agt_grade_area_rules_cd',
  /** 代理商等级 */
  agentGrade = 'agt_grade',

  // 語言
  LanTypeCd = 'lan_type_cd',
  /** 活动领取记录 */
  offerAwardSourceType = 'award_source_type',
  offerAwardName = 'award_name',

  //游戏记录结算状态
  RaCenterSettState = 'ra_center_sett_state',

  /**交易相关 缩略*/
  raTypeCd = 'raTypeCd',
  /**交易相关 全*/
  raDwTypeCd = 'raDwTypeCd',

  /** 财务日志列表 - 类型 - 手续费 */
  raFeeTypeCd = 'raFeeTypeCd',
  /** 财务日志列表 - 类型 - 衍生品 */
  raDerivativeTypeCd = 'raDerivativeTypeCd',
  /** 财务日志列表 - 类型 - 返佣 */
  raCommissionTypeCd = 'raCommissionTypeCd',
  /** 财务日志列表 - 状态 */
  statusInd = 'statusInd',
  /** 代理财务记录 - 类型 */
  raLogTypeCd = 'raLogTypeCd',
  exchange = 'exchange',

  /** 新手福利 */
  newerTaskTypeCd = 'newcomer_benefits',
  /** */
  taskTypeCd = 'task_type',
  /** */
  taskConditionCd = 'task_condition_code',

  /** 充值金额限制 */
  chargeAmountLimitCd = 'charge_amount_limit_cd',

  /** 支付方式 */
  payMethodCd = 'pay_method_cd',
  KindTypeCd = 'kind_type_cd',

  /** 虛擬貨幣鏈 */
  virtualCurrencyChain = 'chain_type',
  // 支援的帳戶類型
  SupportedAccountType = 'account_type',
  /** 打码进度 账变小类取字典 */
  raFinancial = 'ra_financial',
}
