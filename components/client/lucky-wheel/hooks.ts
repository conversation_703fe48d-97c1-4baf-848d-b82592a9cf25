import { useCallback, useEffect, useRef } from 'react'

export const useEsc = (cb: () => void) => {
  useEffect(() => {
    const handleEsc = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        cb()
      }
    }
    window.addEventListener('keydown', handleEsc)

    return () => {
      window.removeEventListener('keydown', handleEsc)
    }
  }, [])
}

export const useStarWheel = (props: {
  isSpinning: boolean
  setIsSpinning: (isSpinning: boolean) => void
  handleStart?: () => Promise<number | undefined>
}) => {
  const wheelRef = useRef<{ handleSpin: (stopIndex: number) => void }>(null)

  const handleStart = useCallback(() => {
    if (props.isSpinning) {
      return
    }

    props?.setIsSpinning(true)

    props
      ?.handleStart?.()
      .then((index) => {
        if (index === undefined) return
        wheelRef.current?.handleSpin(index)
      })
      .catch(() => {
        props?.setIsSpinning(false)
      })
  }, [props])

  return {
    wheelRef,
    handleStart,
  }
}
