'use client'
import ContentHeaderBar from '@/components/client/content-header-bar'
import { useTranslate } from '@tolgee/react'
import { <PERSON><PERSON>, Col, ConfigProvider, Divider, List, Modal, Row } from 'antd'

import EmailIcon from 'public/svg/essetional/email.svg'
import InfoIcon from 'public/svg/essetional/info_bold.svg'
import PhoneIcon from 'public/svg/essetional/verify_phone.svg'
import GoogleIcon from 'public/svg/security/verify_google.svg'
import PasswordIcon from 'public/svg/security/user_icon_coinpassword.svg'
import LockIcon from 'public/svg/side_navigator/icon_lock.svg'
import AntiMailIcon from 'public/svg/essetional/anti-mail.svg'
import CheckGreenIcon from 'public/svg/essetional/check_green.svg'
import RightLinearIcon from 'public/svg/arrow/right_linear.svg'
import NoticemarkIcon from 'public/svg/essetional/noticemark.svg'
import useAccountSecurity from '@/hooks/useAccountSecurity'
import { UserWithdrawalPopEnum } from '@/enums/user'
import { userInformationDesensitization } from '@libs/utils/src'
import { useMemo, useState } from 'react'
import { useRouter } from 'next/navigation'
import SetWithdrawalModal from '@/components/client/set-withdrawal-modal'
import { useStaticTools } from '@/store/static-tools'
import { useUserStore } from '@/store/user'
import { VerifyCodeType } from '@libs/typings/src'
import BindGoogleVerificationModal from './components/bind-google-verification-modal'
import SecondaryVerificationModal from '@/components/client/secodary-verification'
import { useSecondaryVerificationModalStore } from '@/components/client/secodary-verification/store'
import AntiPhishingCodeModal from './components/anti-phishing-code-modal'
import SecurityQuestionModal from './components/security-question-modal'
import DelayModal from '@/components/client/delay-modal'
import LoginNameModal from './components/login-name-modal'

import { useAuth } from '@modules/auth/src'
import KYCUnderReviewModal from './components/kyc-under-review-modal'
import { useAuthStore } from '@modules/auth/src/store'

const PersonalCenterAccountSecurityPage = () => {
  const { t } = useTranslate()
  const { message } = useStaticTools()
  const {
    setWithdrawalModalOpen,
    setSetWithdrawalModalOpen,
    withdrawalModalType,
    setWithdrawalModalType,
    getVerifyBaseInfo,
    isBindAdequate,
    bindGoogleVerificationModalOpen,
    setBindGoogleVerificationModalOpen,
    onSecondaryVerificationSuccess,
    setLinkUrl,
    antiPhishingCodeModalOpen,
    setAntiPhishingCodeModalOpen,
    securityQuestionModalOpen,
    setSecurityQuestionModalOpen,
    isEmailBind,
    isPhoneBind,
    isSetWithdrawalPwd,
    isBindGoogleVerify,
    isEmailEnable,
    isMobileEnable,
    isGoogleEnable,
    verifyBaseInfo,
    isNoWithdrawVerify,
    identityVerificationStatus,
  } = useAccountSecurity()
  const { open: secondaryVerificationModalOpen, setOpen: setSecondaryVerificationModalOpen } =
    useSecondaryVerificationModalStore()
  const { setBusinessType } = useUserStore()
  const { userSetting } = useAuthStore()
  const { data: auth } = useAuth()
  const [modal, contextHolder] = Modal.useModal()
  const [loginNameModalOpen, setLoginNameModalOpen] = useState(false)
  const [kycUnderReviewModalOpen, setKycUnderReviewModalOpen] = useState(false)

  const router = useRouter()

  const securityVerification = useMemo(
    () =>
      [
        {
          key: 'email',
          title: t('personal_center.email_verification', {}),
          isBind: isEmailBind,
          description: t('personal_center.email_verification_content', {}),
          icon: <EmailIcon className="h-[22px] w-[22px] text-bgs-900"></EmailIcon>,
          show: isEmailEnable,
          text: isEmailBind ? userInformationDesensitization(verifyBaseInfo?.verifyInfo?.email) : '',
          actionRender: isEmailBind ? (
            <Button
              color="primary"
              variant="link"
              className="p-0 text-sm leading-[18px]"
              onClick={() => {
                if (isSetWithdrawalPwd) {
                  setBusinessType(VerifyCodeType.VerifyBeforeUpdateMail)
                  setSecondaryVerificationModalOpen(true)
                  setLinkUrl('/personal-center/account-security/email?type=modify')
                } else {
                  message.info(t('personal_center.set_a_withdrawal_password', {}))
                }
              }}
            >
              {t('personal_center.modify', {})}
            </Button>
          ) : (
            <Button
              color="primary"
              variant="link"
              className="p-0 text-sm leading-[18px]"
              onClick={() => {
                router.push('/personal-center/account-security/email?type=bind')
              }}
            >
              {t('personal_center.link_now', {})}
            </Button>
          ),
        },
        {
          key: 'mobile',
          title: t('personal_center.phone_verification', {}),
          isBind: isPhoneBind,
          description: t('personal_center.phone_verification_content', {}),
          icon: <PhoneIcon className="h-[22px] w-[22px] text-bgs-900"></PhoneIcon>,
          show: isMobileEnable,
          text: isPhoneBind
            ? `+${verifyBaseInfo?.verifyInfo?.mobileCountryCd} ${userInformationDesensitization(
                verifyBaseInfo?.verifyInfo?.mobileNumber,
              )}`
            : '',
          actionRender: isPhoneBind ? (
            <Button
              color="primary"
              variant="link"
              className="p-0 text-sm leading-[18px]"
              onClick={() => {
                setBusinessType(VerifyCodeType.VerifyBeforeUpdatePhone)
                setSecondaryVerificationModalOpen(true)
                setLinkUrl('/personal-center/account-security/phone?type=modify')
              }}
            >
              {t('personal_center.modify', {})}
            </Button>
          ) : (
            <Button
              color="primary"
              variant="link"
              className="p-0 text-sm leading-[18px]"
              onClick={() => {
                router.push('/personal-center/account-security/phone?type=bind')
              }}
            >
              {t('personal_center.link_now', {})}
            </Button>
          ),
        },
        {
          key: 'google',
          title: t('personal_center.google_verification', {}),
          isBind: isBindGoogleVerify,
          description: t('personal_center.google_verification_content', {}),
          icon: <GoogleIcon className="h-[22px] w-[22px] text-bgs-900"></GoogleIcon>,
          show: isGoogleEnable,
          text: t('personal_center.linked', {}),
          actionRender: isBindGoogleVerify ? (
            <Button
              color="primary"
              variant="link"
              className="p-0 text-sm leading-[18px]"
              onClick={() => {
                setBusinessType(VerifyCodeType.VerifyBeforeUpdateGoogle)
                setSecondaryVerificationModalOpen(true)
              }}
            >
              {t('personal_center.modify', {})}
            </Button>
          ) : (
            <Button
              color="primary"
              variant="link"
              className="p-0 text-sm leading-[18px]"
              onClick={() => {
                setBindGoogleVerificationModalOpen(true)
              }}
            >
              {t('personal_center.link_now', {})}
            </Button>
          ),
        },
        {
          key: 'withdrawal',
          title: t('personal_center.withdraw_password', {}),
          isBind: isSetWithdrawalPwd,
          description: t('personal_center.withdraw_verification_content', {}),
          icon: <PasswordIcon className="h-[22px] w-[22px] text-bgs-900"></PasswordIcon>,
          text: t('personal_center.already_set', {}),
          noText: t('personal_center.not_set', {}),
          show: true,
          actionRender: (
            <div>
              {isSetWithdrawalPwd ? (
                <div>
                  <Button
                    color="primary"
                    variant="link"
                    className="p-0 text-sm leading-[18px]"
                    onClick={() => {
                      setSetWithdrawalModalOpen(true)
                      setWithdrawalModalType(UserWithdrawalPopEnum.edit)
                    }}
                  >
                    {t('personal_center.modify', {})}
                  </Button>
                  <Divider type="vertical"></Divider>
                  <Button
                    color="primary"
                    variant="link"
                    className="p-0 text-sm leading-[18px]"
                    onClick={() => {
                      if (isBindAdequate) {
                        setBusinessType(VerifyCodeType.ResetSecurityCode)
                        setSecondaryVerificationModalOpen(true)
                      } else {
                        message.info(t('personal_center.least_bind', { count: verifyBaseInfo?.verifyNumber }))
                      }
                    }}
                  >
                    {t('personal_center.reset', {})}
                  </Button>
                </div>
              ) : (
                <Button
                  color="primary"
                  variant="link"
                  className="p-0 text-sm leading-[18px]"
                  onClick={() => {
                    setSetWithdrawalModalOpen(true)
                    setWithdrawalModalType(UserWithdrawalPopEnum.setPwd)
                  }}
                >
                  {t('personal_center.setup', {})}
                </Button>
              )}
            </div>
          ),
        },
      ].filter((e) => e.show),
    [verifyBaseInfo],
  )

  const advancedSettings = [
    {
      key: 'login_name',
      title: t('user.login_name', {}),
      description: t('personal_center.login_name_content', {
        name: auth?.userInfo?.loginName || '-',
      }),
      icon: <LockIcon className="h-[22px] w-[22px] text-bgs-900"></LockIcon>,
      actionRender: Boolean(auth?.userInfo?.loginName) ? (
        <span className="flex items-center gap-2">
          <CheckGreenIcon className="h-4 w-4" />
          <span className="text-sm text-shades-100">{t('personal_center.already_set', {})}</span>
        </span>
      ) : (
        <Button
          color="primary"
          variant="link"
          className="p-0 text-sm leading-[18px]"
          onClick={() => {
            setLoginNameModalOpen(true)
          }}
        >
          {t('personal_center.modify', {})}
        </Button>
      ),
    },
    {
      key: 'login_password',
      title: t('personal_center.login_password', {}),
      description: t('personal_center.login_password_content', {}),
      icon: <LockIcon className="h-[22px] w-[22px] text-bgs-900"></LockIcon>,
      actionRender: (
        <Button
          color="primary"
          variant="link"
          className="p-0 text-sm leading-[18px]"
          onClick={() => {
            router.push('/personal-center/account-security/login-password')
          }}
        >
          {t('personal_center.modify', {})}
        </Button>
      ),
    },
    {
      key: 'anti-phishing-code',
      title: t('personal_center.anti_phishing_code', {}),
      description: t('personal_center.anti_phishing_code_content', {}),
      icon: <AntiMailIcon className="h-[22px] w-[22px] text-bgs-900"></AntiMailIcon>,
      actionRender: (
        <Button
          color="primary"
          variant="link"
          className="p-0 text-sm leading-[18px]"
          onClick={() => {
            setAntiPhishingCodeModalOpen(true)
          }}
        >
          {t('personal_center.setup', {})}
        </Button>
      ),
    },
    {
      key: 'security-question',
      title: t('personal_center.security_question', {}),
      actionRender: auth?.userInfo?.confidentiality ? (
        <span className="flex items-center gap-2">
          <CheckGreenIcon className="h-4 w-4" />
          <span className="text-sm text-shades-100">{t('personal_center.already_set', {})}</span>
        </span>
      ) : (
        <Button
          color="primary"
          variant="link"
          className="p-0 text-sm leading-[18px]"
          onClick={() => {
            setSecurityQuestionModalOpen(true)
          }}
        >
          {t('personal_center.setup', {})}
        </Button>
      ),
    },
    {
      key: 'manage-account',
      title: t('personal_center.manage_account', {}),
      actionRender: (
        <div
          className="cursor-pointer"
          onClick={() => {
            if (isNoWithdrawVerify) {
              setSecondaryVerificationModalOpen(true)
              return
            }
            modal
              .confirm({
                width: 490,
                closable: true,
                title: t('personal_center.manage_account', {}),
                content: (
                  <div
                    className="-mr-3 mt-2 flex cursor-pointer items-center justify-between rounded-lg bg-gs-200 p-4"
                    onClick={() => {
                      router.push('/personal-center/account-security/manage-account')
                    }}
                  >
                    <div className="w-[352px]">
                      <div className="text-sm leading-[18px]">{t('personal_center.delete_account', {})}</div>
                      <div className="mt-2 text-xs leading-4 text-bgs-900">
                        {t('personal_center.delete_account_content', {})}
                      </div>
                    </div>
                    <div>
                      <RightLinearIcon className="h-4 w-4 text-bgs-900"></RightLinearIcon>
                    </div>
                  </div>
                ),
                icon: null,
                footer: null,
              })
              .then(
                () => {},
                () => {},
              )
          }}
        >
          <RightLinearIcon className="h-4 w-4 text-bgs-900"></RightLinearIcon>
        </div>
      ),
    },
    {
      key: 'login-log',
      title: t('personal_center.access_history', {}),
      actionRender: (
        <div
          className="cursor-pointer"
          onClick={() => {
            router.push('/personal-center/account-security/login-log')
          }}
        >
          <RightLinearIcon className="h-4 w-4 text-bgs-900"></RightLinearIcon>
        </div>
      ),
    },
    !(userSetting?.artificialKycStatus?.toString() === '0' && userSetting?.autoKycStatus?.toString() === '0') && {
      key: 'identity-verification',
      title: t('personal_center.identity_verification', {}),
      actionRender:
        identityVerificationStatus === '1' ? (
          <div
            className="flex cursor-pointer items-center gap-2"
            onClick={() => {
              router.push('/personal-center/account-security/identify-verification')
            }}
          >
            <Button color="primary" variant="link" className="p-0 text-sm leading-[18px]">
              {t('personal_center.setup', {})}
            </Button>
            <RightLinearIcon className="h-4 w-4 text-bgs-900"></RightLinearIcon>
          </div>
        ) : identityVerificationStatus === '2' ? (
          <div onClick={() => setKycUnderReviewModalOpen(true)}>
            <span className="flex items-center gap-4 text-sm text-warning-3">
              {t('personal_center.under_review', {})}
              <NoticemarkIcon className="h-4 w-4 text-warning-3"></NoticemarkIcon>
            </span>
          </div>
        ) : identityVerificationStatus === '3' ? (
          <div>
            <span className="flex items-center gap-4 text-sm text-success-3">
              {t('personal_center.verified', {})} <CheckGreenIcon className="h-4 w-4 text-bgs-900"></CheckGreenIcon>
            </span>
          </div>
        ) : null,
    },
  ].filter(Boolean)

  return (
    <ConfigProvider
      theme={{
        token: {
          colorSplit: 'var(--color-bgs-600)',
        },
        components: {
          Divider: {
            colorSplit: 'var(--color-bgs-300)',
          },
          Alert: {
            colorText: 'var(--color-primary-1)',
          },
          Button: {
            controlHeightLG: 44,
          },
          Input: {
            controlHeightLG: 51,
            controlHeight: 40,
            activeBorderColor: 'var(--color-primary-1)',
            activeBg: 'var(--color-gs-200)',
            fontSize: 12,
          },
          Select: {
            controlHeight: 40,
          },
          Form: {
            fontSize: 14,
          },

          InputNumber: {
            controlHeight: 40,
            fontSize: 12,
          },
          Segmented: {
            itemColor: 'var(--color-bgs-900)',
          },
          Skeleton: {
            gradientFromColor: 'var(--color-shades-0)',
            controlHeightXS: 8,
            paragraphLiHeight: 12,
            blockRadius: 0,
          },
        },
      }}
    >
      <div className="m-auto max-w-[1200px]">
        <ContentHeaderBar title={t('personal_center.account_security', {})}></ContentHeaderBar>
        <div className="mt-6">
          <div>
            <div className="mb-6 text-xl font-semibold text-shades-100">
              {t('personal_center.security_verification', {})}
            </div>
            <List
              className="mb-12 bg-gs-200"
              size="large"
              itemLayout="horizontal"
              bordered
              dataSource={securityVerification}
              renderItem={(item) => (
                <List.Item>
                  <Row key="1" className="w-full">
                    <Col span={16}>
                      <div className="flex items-center gap-4">
                        {item.icon}
                        <div>
                          <div className="text-base font-medium leading-5 text-shades-100">{item.title}</div>
                          <div className="mt-2 w-[508px] text-sm leading-[18px] text-bgs-900">{item.description}</div>
                        </div>
                      </div>
                      <div></div>
                    </Col>

                    <Col span={4} className="flex items-center gap-2">
                      {item.isBind ? (
                        <>
                          <CheckGreenIcon className="h-4 w-4" />
                          <span className="text-sm text-shades-100">{item.text}</span>
                        </>
                      ) : (
                        <>
                          <InfoIcon className="h-4 w-4 text-bgs-900"></InfoIcon>
                          <span className="text-sm leading-[18px]">
                            {item.noText || t('personal_center.not_linked', {})}
                          </span>
                        </>
                      )}
                    </Col>

                    <Col span={4} className="flex items-center justify-end">
                      {item.actionRender}
                    </Col>
                  </Row>
                </List.Item>
              )}
            />
          </div>
          <div>
            <div className="mb-6 text-xl font-semibold text-shades-100">
              {t('personal_center.advanced_settings', {})}
            </div>
            <List
              className="mb-12 bg-gs-200"
              size="large"
              itemLayout="horizontal"
              bordered
              dataSource={advancedSettings}
              renderItem={(item) => (
                <List.Item>
                  <div key="1" className="flex w-full justify-between">
                    <div>
                      <div className="flex items-center gap-4">
                        {item.icon}
                        <div>
                          <div className="text-base font-medium leading-5 text-shades-100">{item.title}</div>
                          <div className="mt-2 w-[508px] text-sm leading-[18px] text-bgs-900">{item.description}</div>
                        </div>
                      </div>
                      <div></div>
                    </div>

                    <div className="flex items-center">{item?.actionRender}</div>
                  </div>
                </List.Item>
              )}
            />
          </div>
        </div>
        <DelayModal open={setWithdrawalModalOpen}>
          <SetWithdrawalModal
            type={withdrawalModalType}
            open={setWithdrawalModalOpen}
            setOpen={setSetWithdrawalModalOpen}
            refresh={getVerifyBaseInfo}
          ></SetWithdrawalModal>
        </DelayModal>

        <DelayModal open={secondaryVerificationModalOpen}>
          <SecondaryVerificationModal onSuccess={onSecondaryVerificationSuccess}></SecondaryVerificationModal>
        </DelayModal>

        <DelayModal open={bindGoogleVerificationModalOpen}>
          <BindGoogleVerificationModal
            open={bindGoogleVerificationModalOpen}
            setOpen={setBindGoogleVerificationModalOpen}
            refresh={getVerifyBaseInfo}
          ></BindGoogleVerificationModal>
        </DelayModal>

        <DelayModal open={antiPhishingCodeModalOpen}>
          <AntiPhishingCodeModal
            open={antiPhishingCodeModalOpen}
            setOpen={setAntiPhishingCodeModalOpen}
            refresh={getVerifyBaseInfo}
          ></AntiPhishingCodeModal>
        </DelayModal>

        <DelayModal open={securityQuestionModalOpen}>
          <SecurityQuestionModal
            open={securityQuestionModalOpen}
            setOpen={setSecurityQuestionModalOpen}
            refresh={getVerifyBaseInfo}
          ></SecurityQuestionModal>
        </DelayModal>

        <LoginNameModal
          open={loginNameModalOpen}
          onClose={() => {
            setLoginNameModalOpen(false)
          }}
          onSuccess={() => {
            getVerifyBaseInfo()
          }}
        ></LoginNameModal>

        <KYCUnderReviewModal
          open={kycUnderReviewModalOpen}
          onClose={() => setKycUnderReviewModalOpen(false)}
        ></KYCUnderReviewModal>

        {contextHolder}
      </div>
    </ConfigProvider>
  )
}
export default PersonalCenterAccountSecurityPage
