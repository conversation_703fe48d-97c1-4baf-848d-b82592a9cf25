import type { S3Path } from '../../servers/s3/interface'
import type { FooterType, HeaderType, SliderBarItems, SliderBarType, UserAgreement, CookiePolicy } from '../../typings'

export const s3: Partial<Record<S3Path, any>> = {
  getDb: getDb<PERSON>son(),
  getFooter: {
    contact: 'https://www.google.com',
    license: [
      {
        image: 'https://ab-uat-s3-saas-515.s3-accelerate.amazonaws.com/XGlobal.png',
        url: 'https://www.google.com',
        className: 'h-7 w-36 object-contain',
        width: 144,
        height: 28,
      },
      {
        image: 'https://ab-uat-s3-saas-515.s3-accelerate.amazonaws.com/%E7%BD%91%E9%A1%B5logo.png',
        url: 'https://www.google.com/a',
        className: 'w-7 h-7 object-contain rounded-full',
        width: 28,
        height: 28,
      },
    ],
    licenseNumber: '',
    logo: 'https://picsum.photos/200/200',
    copyright: '&lt;p&gt;&amp;copy;2025XG.IO版权所有&lt;/p&gt;',
    company: `澳門威尼斯人8899.com`,
    issuer: ``,
    linkGroups: [
      {
        groupName: 'Casino',
        links: [
          {
            code: 'tasks',
            name: 'Tasks',
          },
          {
            code: 'rebate',
            name: 'Rebate',
          },
          {
            code: 'invite',
            name: 'Invite',
          },
        ],
      },
      {
        groupName: 'Games',
        links: [
          {
            code: 'fishing',
            name: 'Fishing',
          },
          {
            code: 'electronic',
            name: 'Electronic',
          },
          {
            code: 'card_board',
            name: 'Card_board',
          },
        ],
      },
      {
        groupName: 'Support',
        links: [
          {
            code: 'online_support',
            name: 'Online_support',
          },
        ],
      },
    ],
    providers: [],
    // providers: [
    //   {
    //     name: 'Provider 1',
    //     url: '/provider/1',
    //     image: 'https://saas-s3-prod.s3-accelerate.amazonaws.com/xgaming-1730282488709.png',
    //     width: 105,
    //     height: 28,
    //     className: 'h-7 w-28 object-contain',
    //   },
    //   {
    //     name: 'Provider 2',
    //     url: '/provider/2',
    //     image: 'https://saas-s3-prod.s3-accelerate.amazonaws.com/xgaming-1730282488709.png',
    //     width: 105,
    //     height: 28,
    //     className: 'h-7 w-28 object-contain',
    //   },
    //   {
    //     name: 'Provider 3',
    //     url: '/provider/3',
    //     image: 'https://saas-s3-prod.s3-accelerate.amazonaws.com/xgaming-1730282488709.png',
    //     width: 105,
    //     height: 28,
    //     className: 'h-7 w-28 object-contain',
    //   },
    //   {
    //     name: 'Provider 4',
    //     url: '/provider/4',
    //     image: 'https://saas-s3-prod.s3-accelerate.amazonaws.com/xgaming-1730282488709.png',
    //     width: 105,
    //     height: 28,
    //     className: 'h-7 w-28 object-contain',
    //   },
    //   {
    //     name: 'Provider 5',
    //     url: '/provider/5',
    //     image: 'https://saas-s3-prod.s3-accelerate.amazonaws.com/xgaming-1730282488709.png',
    //     width: 105,
    //     height: 28,
    //     className: 'h-7 w-28 object-contain',
    //   },
    //   {
    //     name: 'Provider 6',
    //     url: '/provider/6',
    //     image: 'https://saas-s3-prod.s3-accelerate.amazonaws.com/xgaming-1730282488709.png',
    //     width: 105,
    //     height: 28,
    //     className: 'h-7 w-28 object-contain',
    //   },
    //   {
    //     name: 'Provider 7',
    //     url: '/provider/7',
    //     image: 'https://saas-s3-prod.s3-accelerate.amazonaws.com/xgaming-1730282488709.png',
    //     width: 105,
    //     height: 28,
    //     className: 'h-7 w-28 object-contain',
    //   },
    //   {
    //     name: 'Provider 8',
    //     url: '/provider/8',
    //     image: 'https://saas-s3-prod.s3-accelerate.amazonaws.com/xgaming-1730282488709.png',
    //     width: 105,
    //     height: 28,
    //     className: 'h-7 w-28 object-contain',
    //   },
    //   {
    //     name: 'Provider 9',
    //     url: '/provider/9',
    //     image: 'https://saas-s3-prod.s3-accelerate.amazonaws.com/xgaming-1730282488709.png',
    //     width: 105,
    //     height: 28,
    //     className: 'h-7 w-28 object-contain',
    //   },
    //   {
    //     name: 'Provider 10',
    //     url: '/provider/10',
    //     image: 'https://saas-s3-prod.s3-accelerate.amazonaws.com/xgaming-1730282488709.png',
    //     width: 105,
    //     height: 28,
    //     className: 'h-7 w-28 object-contain',
    //   },
    // ],
    socialMedias: [
      {
        name: 'WhatsApp',
        url: 'https://xg.io/',
        icon: 'https://ab-uat-s3-saas-515.s3.ap-southeast-1.amazonaws.com/ns3/com/foot/media/whatsapp.png',
      },
      {
        name: 'QQ',
        url: 'https://xg.io/en-US',
        icon: 'https://ab-uat-s3-saas-515.s3.ap-southeast-1.amazonaws.com/ns3/com/foot/media/qq.png',
      },
    ],
  } satisfies FooterType,
  getUserAgreement: {
    icon: '18+',
    html: '&lt;p&gt;&lt;strong&gt;澳門威尼斯人8899.com&lt;/strong&gt;&lt;/p&gt;',
  } satisfies UserAgreement,
  getHeader: [
    {
      type: 'logo',
      showWhen: 'always',
      image: 'https://ab-uat-s3-saas-515.s3.ap-southeast-1.amazonaws.com/ns3/zz/logo/logo1.png',
    },
    {
      type: 'balance',
      showWhen: 'logged',
    },
    {
      type: 'auth',
      showWhen: 'always',
      config: [
        {
          type: 'login-register',
          buttonType: 'default',
          showWhen: 'unlogged',
        },
        {
          type: 'userMenu',
          showWhen: 'logged',
          menuItems: [
            {
              type: 'asset',
              name: 'header.assets', //资产总览
              href: '/account-assets',
            },
            {
              type: 'betting',
              name: 'header.betting', //投注记录
              href: '/betting-record',
            },
            {
              type: 'turnoverList',
              name: 'header.turnover', //打码进度
              href: '/turnover-list',
            },
            {
              type: 'fiatCurrencyOrderHistory',
              name: 'silderBar.fiat_currency_order_history', // 法幣訂單歷史
              href: '/fiat-currency-order-history',
            },
            {
              name: 'silderBar.fundTransfer', // 虛擬幣充提
              type: 'fundTransfer',
              href: '/crypto',
            },
            {
              type: 'vip',
              name: 'header.vip', //VIP中心
              href: '/promo?events=event_vip',
            },
            {
              type: 'security',
              name: 'header.security', //账户安全
              href: '/personal-center/account-security',
            },
            {
              type: 'invite',
              name: 'header.inviteRebate', //邀请返利
              href: '/agent/invite',
            },
            {
              type: 'alliance',
              name: 'header.agencyCenter', // 联盟中心
              href: '/agent/center',
            },
            {
              type: 'promotions',
              name: 'header.promotions', //优惠活动
              href: '/promo',
            },
            {
              type: 'settings',
              name: 'header.preferenceSettings', //账户安全
              href: '/personal-center/settings', //偏好设置
            },
          ],
        },
        {
          type: 'notice',
          showWhen: 'always',
        },
        {
          type: 'signIn',
          showWhen: 'logged',
        },
        {
          type: 'language',
          showWhen: 'always',
        },
      ],
    },
  ] satisfies HeaderType,
  getSidebar: {
    menus: [
      getCasinoList(),
      getSports(),
      getGameProvider(),
      getOtherItem(),
      getLanguages()
    ],
  } satisfies SliderBarType,
  getAds: [
    {
      adsType: 'google',
      gtagId: 'G-2H5FKPBYBE',
    },
  ] satisfies {
    adsType: 'google' | 'facebook'
    // google 的
    gtagId?: string

    // facebook 的
    metaPixelId?: string
    accessToken?: string
  }[],
  getCookiePolicy: {
    html: '&lt;p&gt;To use certain features, users need to accept the cookie policy.&lt;/p&gt;',
  } satisfies CookiePolicy,
}

/** DB.json 的 mock 資料 */
export function getDbJson() {
  return {
    layout: { title: 'layout:zz:v1', style: {} },
    header: { title: 'header:zz:v1', style: {} },
    menu: { title: 'menu:zz:v1', style: {} },
    navbar: {
      title: 'navbar:zz:v1',
      style: {},
      items: [
        { sort: 1, code: 'home' },
        { sort: 2, code: 'promo' },
        { code: 'wallet', sort: 2 },
        { sort: 3, code: 'agent' },
        { sort: 4, code: 'profile' },
      ],
      beforeLoginItems: [
        { sort: 1, code: 'home' },
        { sort: 2, code: 'promo' },
        { code: 'wallet', sort: 2 },
        { sort: 3, code: 'notification' },
        { sort: 4, code: 'support' },
      ],
    },
    page: {
      home: {
        title: 'home:zz:v1',
        style: {},
        components: [
          { title: 'banner:zz:v1', style: {} },
          { title: 'marquee:zz:v1', style: {} },
          { title: 'game-group:zz:v1', style: {} },
          { title: 'app-download:zz:v0', style: {} },
        ],
      },
      profile: {
        title: 'profile:zz:v1',
        style: {},
        components: [
          { title: 'user-info:zz:v1', style: {} },
          { title: 'vip-info:zz:v1', style: {} },
          { title: 'agent:zz:v1', style: {} },
          { title: 'wallet-balance:zz:v1', style: {} },
          { title: 'setting:zz:v1', style: {} },
        ],
      },
    },
    config: {
      title: 'template:zz',
      walletMode: 'asia-single',
      swap: 'open',
      custodial: 'open',
      businessId: '240053',
      cookiePolicy: 'open',
      seo: {
        title: 'æ¾³é—¨æ–°è‘¡äº¬ | åœ¨çº¿å¨±ä¹é¦–é€‰å¹³å° | é«˜ç«¯å®‰å…¨ç¨³å®šçš„åšå½©ä½“éªŒæ–°è‘¡äº¬',
        keywords:
          'æ–°è‘¡äº¬, æ–°è‘¡äº¬å¨±ä¹, æ–°è‘¡äº¬çº¿ä¸Šå¹³å°, åœ¨çº¿åšå½©, åœ¨çº¿èµŒåœº, çœŸäººå¨±ä¹, ä½“è‚²æŠ•æ³¨, è€è™Žæœº, ä¿¡èª‰å¹³å°, åœ¨çº¿å¨±ä¹åŸŽæ–°è‘¡äº¬, æ–°è‘¡äº¬å¨±ä¹, æ–°è‘¡äº¬çº¿ä¸Šå¹³å°, åœ¨çº¿åšå½©, åœ¨çº¿èµŒåœº, çœŸäººå¨±ä¹, ä½“è‚²æŠ•æ³¨, è€è™Žæœº, ä¿¡èª‰å¹³å°, åœ¨çº¿å¨±ä¹åŸŽæ–°è‘¡äº¬, æ–°è‘¡äº¬å¨±ä¹, æ–°è‘¡äº¬çº¿ä¸Šå¹³å°, åœ¨çº¿åšå½©, åœ¨çº¿èµŒåœº, çœŸäººå¨±ä¹, ä½“è‚²æŠ•æ³¨, è€è™Žæœº, ä¿¡èª‰å¹³å°, åœ¨çº¿å¨±ä¹åŸŽæ–°è‘¡äº¬, æ–°è‘¡äº¬å¨±ä¹, æ–°è‘¡äº¬çº¿ä¸Šå¹³å°, åœ¨çº¿åšå½©',
        description: 'æ¾³é—¨æ–°è‘¡äº¬',
        image: 'https://saas-s3-staging.s3-accelerate.amazonaws.com/dark%20clearspace_x%20global_h-1744904842028.png',
        meta: 'æ–°è‘¡äº¬, æ–°è‘¡äº¬å¨±ä¹, æ–°è‘¡äº¬çº¿ä¸Šå¹³å°, åœ¨çº¿åšå½©, åœ¨çº¿èµŒåœº',
      },
      brand: {
        image: {
          favicon:
            'https://saas-s3-staging.s3-accelerate.amazonaws.com/favicon-xg-32x32%E6%8B%B7%E8%B2%9D1744983548549',
          logo: 'https://saas-s3-staging.s3-accelerate.amazonaws.com/bright%20clearspace_x%20global_h_tr1744983414888',
          darkLogo:
            'https://saas-s3-staging.s3-accelerate.amazonaws.com/dark%20clearspace_x%20global_h_tr_1745047836607.png',
        },
        notAllow: {
          image: 'https://saas-s3-staging.s3-accelerate.amazonaws.com/x%20global%20mono_black_h1744983662811',
          color: 'black',
          html: '&lt;div style=&quot;text-align: center;&quot;data-zone-id=&quot;0 &quot;data-line-index=&quot;0 &quot;data-line=&quot;true &quot;&gt;&lt;strong &gt;Access Restricted:&lt;br /&gt;IP location is not within our service area.&lt;/strong &gt;&lt;/div &gt;',
        },
        aboutUs: 'https://saas-s3-staging.s3-accelerate.amazonaws.com/240053/zh-CN/sub/aboutUs.json',
        userAgreement: 'https://saas-s3-staging.s3-accelerate.amazonaws.com/240053/zh-CN/sub/agreement.json',
        footer: 'https://saas-s3-staging.s3-accelerate.amazonaws.com/240053/zh-CN/sub/aboutUs.json',
        name: 'XSaaS Progressive Web App',
      },
      pwa: {
        icons: [
          { src: 'https://saas-s3-staging.s3-accelerate.amazonaws.com/48x48.png', type: 'image/png', sizes: '48x48' },
          {
            src: 'https://saas-s3-staging.s3-accelerate.amazonaws.com/512x512.png',
            type: 'image/png',
            sizes: '512x512',
          },
          {
            src: 'https://saas-s3-staging.s3-accelerate.amazonaws.com/256x256.png',
            type: 'image/png',
            sizes: '256x256',
          },
          {
            src: 'https://saas-s3-staging.s3-accelerate.amazonaws.com/128x128.png',
            type: 'image/png',
            sizes: '128x128',
          },
          { src: 'https://saas-s3-staging.s3-accelerate.amazonaws.com/96x96.png', type: 'image/png', sizes: '96x96' },
          { src: 'https://saas-s3-staging.s3-accelerate.amazonaws.com/72x72.png', type: 'image/png', sizes: '72x72' },
        ],
        name: 'XSaaS Progressive Web App',
        short_name: 'XGlobal',
        description: 'A Progressive Web App built with XSaaS.',
        popup_description: '',
        background_color: 'black',
        theme_color: 'black',
      },
      vip: { isHidden: true },
      agent: { isHidden: true },
      style: { css: 'zz' },
    },
  }
}

/** 側邊欄 start */
function getCasinoList(): SliderBarItems {
  return {
    title: {
      name: 'silderBar.casino', // 常用的
      defaultShow: true,
      showArrow: true,
      icon: 'casino',
      active: false,
      expand: true,
      route: '/home',
    },
    menus: [],
  }
}

function getSports(): SliderBarItems {
  return {
    title: {
      name: 'silderBar.sports', // 常用的
      defaultShow: true,
      showArrow: false,
      icon: 'sports',
      active: false,
      expand: false,
      route: '',
    },
    menus: [],
  }
}

function getGameProvider(): SliderBarItems {
  return {
    title: {
      name: 'silderBar.gameProvider', // 常用的
      defaultShow: true,
      showArrow: false,
      icon: 'gameProvider',
      active: false,
      expand: false,
      route: '/mini-game/game-provider',
    },
    menus: [],
  }
}

function getOtherItem(): SliderBarItems {
  return {
    menus: [
      {
        name: 'silderBar.vipClub',
        icon: 'vipClub',
        route: '/promo?events=event_vip',
        defaultShow: true,
        active: false,
      },
      {
        name: 'silderBar.promotions',
        icon: 'promotions',
        route: '/promo',
        defaultShow: true,
        active: false,
      },
      // {
      //   name: 'silderBar.sponsership',
      //   icon: 'sponsership',
      //   route: '',
      //   defaultShow: false,
      //   active: false,
      // },
      {
        name: 'silderBar.liveSupport',
        icon: 'liveSupport',
        route: '',
        defaultShow: true,
        active: false,
      }
    ]
  }
}

function getLanguages(): SliderBarItems {
  return {
    title: {
      name: 'silderBar.language',
      defaultShow: true,
      showArrow: true,
      icon: 'language',
      active: false,
      expand: false,
      route: '',
    },
    menus: [],
  }
}
