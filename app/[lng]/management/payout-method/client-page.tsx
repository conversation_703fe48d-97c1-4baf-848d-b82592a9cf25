'use client'

import { useEffect, useState } from 'react'

import { useTranslate } from '@tolgee/react'
import { <PERSON>ert, Button, ConfigProvider, Empty, Space, Switch, Table, TableProps } from 'antd'

import {
  createApi,
  GetCanPaymentListResponse,
  type PaymentAccountOptions,
  type SetEnablePaymentParams,
} from '@libs/apis'

import ContentHeaderBar from '@/components/client/content-header-bar'
import AddPayoutAccountModal from './components/add-payout-account'
import DeletePayoutAccountModal from './components/delete-payout-account'
import useSWRMutation from 'swr/mutation'

import { CodeDetail } from '@libs/typings'
import CodeDict from '@/components/client/code-dicts'
import { cn } from '@libs/utils'

import useSWR from 'swr'
import EmptyContent from '@/components/client/empty-content'

import { useSecondaryVerificationModalStore } from '@/components/client/secodary-verification/store'
import NoWithdrawConfirm from '@/components/client/secodary-verification/no-withdraw-confirm'

export default function PayoutMethodClientPage() {
  const { t } = useTranslate()

  const { getVerifyBaseInfo } = useSecondaryVerificationModalStore()

  const [openAddPayoutAccountModal, setOpenAddPayoutAccountModal] = useState(false)
  const [editItem, setEditItem] = useState<PaymentAccountOptions | undefined>(undefined)

  const [deleteItem, setDeleteItem] = useState<PaymentAccountOptions | undefined>(undefined)
  const [openDeletePayoutAccountModal, setOpenDeletePayoutAccountModal] = useState(false)
  const [openNoWithdrawConfirmModal, setOpenNoWithdrawConfirmModal] = useState(false)

  const { trigger: getVerifyBaseInfoFunc } = useSWRMutation('getVerifyBaseInfo', getVerifyBaseInfo)
  const { data: payoutMethods, trigger: getPayoutMethods } = useSWRMutation(
    `PayoutMethodClientPage-${openAddPayoutAccountModal}`,
    async () => {
      const { data: addressList } = await createApi().apis.finance.getPaymentAccountOptions()
      return addressList?.recivePaymentList || []
    },
  )

  const { data: allPaymentList } = useSWR('allPaymentList', async () => {
    const { data: currencyList } = await createApi().apis.finance.getCanPaymentList()
    return currencyList || []
  })

  const handleOpenAddPayoutAccountModalFunc = async (item?: PaymentAccountOptions) => {
    setEditItem(item)

    const { isNoWithdrawVerify } = await getVerifyBaseInfoFunc()
    if (isNoWithdrawVerify) {
      setOpenNoWithdrawConfirmModal(true)
      return
    }

    setOpenAddPayoutAccountModal(true)
  }

  useEffect(() => {
    if (openAddPayoutAccountModal) {
      return
    }
    getPayoutMethods()
  }, [openAddPayoutAccountModal])

  return (
    <div className="min-h-[50vh] px-8">
      <div className="mx-auto max-w-[1200px]">
        <ContentHeaderBar title={t('payout_method.title', {})}>
          <Button
            type="primary"
            size="large"
            onClick={() => {
              handleOpenAddPayoutAccountModalFunc()
            }}
          >
            {t('payout_method.add_payout_method', {})}
          </Button>
        </ContentHeaderBar>

        <Alert
          className="my-4"
          message={<span className="text-sm text-primary-1">{t('payout_method.alert', {})}</span>}
          type="info"
          showIcon
        />

        {!payoutMethods?.length ? (
          <EmptyPayoutMethod
            onAdd={() => {
              handleOpenAddPayoutAccountModalFunc()
            }}
          />
        ) : (
          <PayoutMethodList
            list={payoutMethods}
            allPaymentList={allPaymentList}
            onEdit={(item) => {
              handleOpenAddPayoutAccountModalFunc(item)
            }}
            onDelete={(item) => {
              setDeleteItem(item)
              setOpenDeletePayoutAccountModal(true)
            }}
            onChangeEnable={() => {
              getPayoutMethods()
            }}
          />
        )}

        <AddPayoutAccountModal
          editItem={editItem}
          open={openAddPayoutAccountModal}
          allPaymentList={allPaymentList}
          onClose={() => setOpenAddPayoutAccountModal(false)}
        />
        <DeletePayoutAccountModal
          deleteItem={deleteItem}
          open={openDeletePayoutAccountModal}
          onCancel={() => setOpenDeletePayoutAccountModal(false)}
          onSubmit={getPayoutMethods}
        />
        <NoWithdrawConfirm open={openNoWithdrawConfirmModal} onCancel={() => setOpenNoWithdrawConfirmModal(false)} />
      </div>
    </div>
  )
}

// 空状态
function EmptyPayoutMethod({ onAdd }: { onAdd: () => void }) {
  const { t } = useTranslate()
  return (
    <div className="flex min-h-[500px] flex-col items-center justify-center">
      <Empty className="mb-4" description={t('payout_method.empty_payout_method', {})} />
      <Button variant="outlined" color="primary" size="large" onClick={onAdd}>
        + {t('payout_method.add_payout_method2', {})}
      </Button>
    </div>
  )
}

// 列表
function PayoutMethodList({
  list,
  allPaymentList,
  onChangeEnable,
  onEdit,
  onDelete,
}: {
  list: PaymentAccountOptions[]
  allPaymentList?: GetCanPaymentListResponse
  onEdit?: (item: PaymentAccountOptions) => void
  onDelete?: (item: PaymentAccountOptions) => void
  onChangeEnable?: (id: string, enabled: number) => void
}) {
  const { t } = useTranslate()

  const [loadingId, setLoadingId] = useState<string | null>(null)
  const { trigger: setEnablePayment } = useSWRMutation(
    `PayoutMethodClientPage-setEnablePayment`,
    async (_, { arg }: { arg: SetEnablePaymentParams }) => {
      await createApi().apis.finance.setEnablePayment({ data: arg })
      onChangeEnable?.(arg.id, arg.enabled)
      return arg.enabled
    },
  )

  const columns: TableProps<PaymentAccountOptions>['columns'] = [
    {
      title: t('payout_method.coin_method', {}),
      dataIndex: 'paymentTypeCd',
      width: '20%',
      render: (text) => {
        return <CodeDict type={CodeDetail.payMethodCd} textId={text?.toUpperCase() || ''} />
      },
    },
    {
      title: t('payout_method.account', {}),
      width: '20%',
      dataIndex: 'infoAlias',
    },
    {
      title: t('payout_method.account_details', {}),
      dataIndex: 'coinId',
      width: '40%',
      render: (text, item) => {
        const payment = allPaymentList?.find((item) => String(item.id) === text)
        return (
          <div className="flex items-center gap-2">
            <span className="rounded-md bg-gs-300 px-2 py-1 text-xs text-bgs-900">{payment?.coinFullName}</span>
            <span className="text-sm text-gs-900">{item.account}</span>
          </div>
        )
      },
    },
    {
      title: t('payout_method.actions', {}),
      dataIndex: 'enabled',
      align: 'center',
      render: (enabled, item) => (
        <Space size="small" className="flex items-center justify-center gap-2">
          <div className="cursor-pointer text-sm text-primary-1" onClick={() => onEdit?.(item)}>
            {t('payout_method.edit', {})}
          </div>
          <div className="cursor-pointer text-sm text-error-4" onClick={() => onDelete?.(item)}>
            {t('payout_method.delete', {})}
          </div>
          <div
            className={cn('text-sm', {
              'text-success-2': enabled === 1,
              'text-gs-700': enabled === 0,
            })}
          >
            {enabled === 1 ? t('payout_method.enabled', {}) : t('payout_method.disabled', {})}
          </div>
          <Switch
            size="small"
            checked={enabled === 1}
            loading={loadingId === item.id}
            onChange={(checked) => {
              setLoadingId(item.id)
              setEnablePayment({ id: item.id, enabled: checked ? 1 : 0 }).finally(() => {
                setLoadingId(null)
              })
            }}
          />
        </Space>
      ),
    },
  ]
  return (
    <ConfigProvider
      theme={{
        components: {
          Table: {
            colorBgBase: 'var(--color-gs-200)',
            colorText: 'var(--color-bgs-900)',
            headerBorderRadius: 10,
          },
        },
      }}
    >
      <Table
        dataSource={list}
        columns={columns}
        rowKey="id"
        pagination={false}
        tableLayout="fixed"
        locale={{
          emptyText: <EmptyContent />,
        }}
      />
    </ConfigProvider>
  )
}
