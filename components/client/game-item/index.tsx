import { message, Skeleton } from 'antd'
import { CSSProperties, useMemo, useState } from 'react'
import { useParams, useRouter } from 'next/navigation'

import Icon from '@/components/client/icon'
import { GameImage } from '@/components/client/game-image'

import { formatAmount } from '@/utils/func'
import { useTranslate } from '@tolgee/react'
import { GameListItem, SidebarGameItem } from '@libs/apis'

import { getImageUrl } from './get-Image-url'
import { YesOrNo } from '@/enums/common'
import { cn } from '@libs/utils/src'
import { useAuth } from '@modules/auth/src'
import { useUserStore } from '@/store/user'
import { TabsTypeLogRegisterType } from '@/enums/login'
import { Profile } from 'iconsax-react'
import Construction from '@/public/svg/game/construction.svg'
import { useAuthStore } from '@modules/auth/src/store'

type Props = {
  className?: string
  style?: CSSProperties
  disabled?: boolean
  data: SidebarGameItem | GameListItem
  isLoading?: boolean
  onlyImg?: boolean
  onClick?: () => void
}

function GameItem({ className, style, disabled, data, onlyImg, isLoading, onClick }: Props) {
  const router = useRouter()
  const { t } = useTranslate()
  const { lng } = useParams<{ lng: string }>()
  const [isError, setIsError] = useState(false)
  const [isHover, setIsHover] = useState(false)
  const { isLogin } = useAuth()
  const { userInfo } = useAuthStore()
  const { setRegisterLoginModalActiveKey, setLogRegisterVisible } = useUserStore()
  const isAbnormal = !data?.isCurrentRegion || data?.maintenanceStatusCd !== 'normal'
  const isSupportAnonymous = data?.isSupportAnonymous === 1
  const gameImageUrl = useMemo(() => {
    const isGroup = !data?.id && data?.groupId

    if (isGroup) {
      return getImageUrl({
        isGroup: data.isGroup,
        groupCode: data.groupCode,
        code: data.code || '1',
        lng,
      })
    }

    return data?.pcThumbnail || data?.mobileThumbnail || ''
  }, [data])

  if (isLoading || !gameImageUrl || isError) {
    return <Skeleton.Image active={!isError} style={{ minHeight: '217px', width: '100%' }} />
  }

  // 检查是否为游戏组或厂商
  const isGroupOrProvider = data.isGroup

  return (
    <div
      className={cn(
        'flex flex-1 flex-col transition-all duration-[.4s]',
        {
          'hover:-translate-y-2': !disabled,
        },
        className,
      )}
      onClick={() => onClick && onClick()}
      onMouseEnter={() => setIsHover(true)}
      onMouseLeave={() => setIsHover(false)}
      style={style}
    >
      <div className="relative flex flex-1 items-center">
        {/* {isSupportAnonymous && (
          <div className="flashing absolute right-0 top-0 z-1 flex items-center">
            <button
              className="relative inline-block !h-[23px] w-full rounded-bl-lg rounded-tr-lg bg-primary-1 px-2 py-1 text-[10px] font-medium leading-[10px] !text-on-button-primary"
              onClick={onClick}
            >
              {t('try_now', {})}
            </button>
          </div>
        )} */}

        <GameImage
          src={gameImageUrl}
          alt={gameImageUrl}
          isGroup={data.isGroup}
          direct={data.direct}
          disabled={disabled}
          isSupportAnonymous={isSupportAnonymous}
          isAbnormal={isAbnormal}
          isHover={isHover && !isGroupOrProvider} // 游戏组或厂商不显示滑入遮挡
          onError={() => {
            setIsError(true)
          }}
          onClick={() => {
            console.log('onClick')
            if (disabled) {
              return
            }

            const { id, groupId = '', isGroup, direct, groupName } = data

            if (isGroup && direct === YesOrNo.No) {
              router.push(`/mini-game/game-shows?groupId=${groupId}&title=${groupName}`)
            } else {
              if (!isLogin && data?.isSupportAnonymous === 2) {
                setLogRegisterVisible(true)
                setRegisterLoginModalActiveKey(TabsTypeLogRegisterType.login)
              } else {
                if (userInfo?.selfLimitStatus === 1 && userInfo?.globalSelfLimitGameStatus === 1) {
                  message.error(t('common.remove_self_restrictions', {}))
                } else {
                  router.push(`/mini-game/details/${id || 'isGroup'}?groupId=${groupId}`)
                }
              }
            }
          }}
        />

        {isAbnormal && (
          <div className="absolute inset-0 z-10 flex flex-col items-center justify-center overflow-hidden rounded-lg bg-bgs-100">
            {!data.isCurrentRegion ? (
              <Icon name="Lock" className="text-bgs-900" />
            ) : (
              <Construction className="h-4 !text-bgs-900" />
            )}
            <div className="mt-1 w-[72%] text-center text-sm">
              {!data.isCurrentRegion
                ? t('features_home_component_disable_game_index_2vv8jhk4ek', {})
                : t('features_entertainment_area_quota_conversion_index_ueleprpp22', {})}
            </div>
          </div>
        )}

        {/* 在线人数 - 只在普通游戏卡片(非游戏组/厂商)且非onlyImg模式下显示 */}
        {!onlyImg && !isGroupOrProvider && (
          <div className="absolute bottom-[6px] right-[6px] flex h-[16px] items-center justify-center rounded bg-[#444444] bg-opacity-90 px-1">
            <Profile color="#ffffff" variant="Bold" size={10} />
            <span className="mx-1 text-[10px] leading-[10px] text-white">{formatAmount(data.livePt, 0)}</span>
          </div>
        )}
      </div>
      {/* {!onlyImg && ( */}
      <div className="mt-1 flex w-full basis-5 items-center justify-center pl-px text-xs font-semibold">
        <span className="text-shades-100">{data.name}</span>
      </div>
      {/* )} */}
    </div>
  )
}

export default GameItem
