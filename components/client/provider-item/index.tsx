import { cn } from '@libs/utils/src'
import { Skeleton } from 'antd'
import { GameGroup } from '@libs/apis'
import { useRouter } from 'next/navigation'
import CustomImage from '@/components/server/image'

const useProviderJump = () => {
  const router = useRouter()

  return (id: number, name: string) => {
    router.push(`/mini-game/game-shows?supplierId=${id}&title=${name}`)
  }
}

type Props = {
  data: GameGroup
  isDisabled: boolean
  className?: string
  isLoading?: boolean
  skeletonStyle?: { maxWidth: number; height: number }
  onClick?: () => void
}

export default function ProviderItem({
  data,
  isDisabled,
  className = '',
  isLoading,
  skeletonStyle = { maxWidth: 161, height: 60 },
  onClick,
}: Props) {
  const providerJump = useProviderJump()

  if (!data.logo || isLoading) return <Skeleton.Image active style={skeletonStyle} />

  return (
    <div
      className={cn(
        'flex h-[60px] cursor-pointer items-center justify-center overflow-hidden rounded-lg px-4 py-1 transition-all duration-[.4s]',
        isDisabled ? 'bg-bgs-100 grayscale-[70%]' : 'bg-gs-200 hover:-translate-y-2 hover:bg-bgs-200',
        className,
      )}
      onClick={() => {
        onClick && onClick()
        providerJump(data.id, data.name)
      }}
    >
      <CustomImage className="w-full object-contain" src={data.logo} unoptimized alt={data.name} />
    </div>
  )
}
