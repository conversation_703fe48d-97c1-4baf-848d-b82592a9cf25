'use client'

import Image from 'next/image'
import CDCard from '@/public/svg/game/GamaIcon7.svg'
import useRecentGameHistory from './recentGameHooks'
import { memo } from 'react'
import { SidebarGameItem } from '@libs/apis/src'
import GlobalSearch from '../home/<USER>/home/<USER>'
import ContentHeaderBar from '@/components/client/content-header-bar'
import SearchLoad from '@/components/client/load-more'
import GameShowsList from '@/components/client/game-show-list/game-show-list'
import { useTranslate } from '@tolgee/react'

// 遊戲卡片 等待對接遊戲跳轉詳情
const GameCard = memo(({ game }: { game: SidebarGameItem }) => (
  <div key={game.id} className="group relative aspect-[4/5] cursor-pointer overflow-hidden rounded-lg">
    <Image src={game.pcThumbnail} alt={game.name ?? ''} fill className="object-fill" />
  </div>
))

GameCard.displayName = 'GameCard'

export default function RecentGameHistory() {
  const { t } = useTranslate()

  const { gameList, total, isGameListLoading, pageNum, setPageNum } = useRecentGameHistory()

  return (
    <div className="m-auto flex max-w-[1200px] flex-col">
      <ContentHeaderBar title={t('silderBar.recentGame', {})} Icon={CDCard} />

      <div className="flex flex-col gap-8 py-8">
        <GlobalSearch />
        <GameShowsList gameList={gameList || []} isGameListLoading={isGameListLoading} />
        <SearchLoad
          total={total || 0}
          pageSize={gameList.length || 0}
          pageNum={pageNum}
          isGameListLoading={isGameListLoading}
          onLoad={setPageNum}
        />
      </div>
    </div>
  )
}
