'use client'

import { cn } from '@libs/utils/src'
import { motion } from 'motion/react'
import Image from 'next/image'
import { useRef, useState } from 'react'

// images
import ImageBackground from '@/components/client/lucky-wheel/images/landlord/background.png'
import CloseIcon from '@/components/client/lucky-wheel/images/landlord/button-close.png'
import MusicOffIcon from '@/components/client/lucky-wheel/images/landlord/button-music-off.png'
import MusicIcon from '@/components/client/lucky-wheel/images/landlord/button-music.png'

// audios
import { backgroundMusic1, closeCancelAudio } from '@/components/client/lucky-wheel/audios'

// utils
import { formatNumber } from '@/libs/apis/src/utils'

// components
import { BtnShare, BtnStart } from './buttons'
import { TextLoseImage, TextWinImage } from './texts'
import { TitleLoseImage, TitleWinImage } from './title'

// types
import { BackdropProps, BaseCardProps, CardProps } from '@/components/client/lucky-wheel/types'

// constants
const DEFAULT_WIDTH = 323
const DEFAULT_LOCALE = 'en'

export function Backdrop({ className, children, ...props }: BackdropProps) {
  return (
    <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
      <div {...props} style={{ backgroundColor: 'rgba(0, 0, 0, 0.67)' }} className={cn('absolute inset-0', className)}>
        {children}
      </div>
    </motion.div>
  )
}

export function BaseCard({ width, children, className, buttonsDivClassName, onClose, ...props }: BaseCardProps) {
  const musicRef = useRef(backgroundMusic1)
  const [isMusicOn, setIsMusicOn] = useState(!musicRef.current.paused)

  const handleMusicOn = () => {
    setIsMusicOn(true)
    musicRef.current.play()
  }

  const handleMusicOff = () => {
    setIsMusicOn(false)
    musicRef.current.pause()
  }

  const handleClose = () => {
    handleMusicOff()
    closeCancelAudio.play()
    onClose?.()
  }

  return (
    <motion.div initial={{ opacity: 0, scale: 0 }} animate={{ opacity: 1, scale: 1 }} exit={{ opacity: 0, scale: 0 }}>
      <div {...props} role="landlord-base-card" style={{ width: `${width}px` }} className={cn('relative', className)}>
        {/* background */}
        <Image src={ImageBackground} alt="bg-image" />

        {/* content */}
        <div className="absolute inset-0">{children}</div>

        {/* buttons */}
        <div className={cn('absolute inset-x-0 top-2 flex items-center justify-between px-3', buttonsDivClassName)}>
          {isMusicOn ? (
            <Image src={MusicIcon} alt="music-icon" className="w-4" onClick={handleMusicOff} />
          ) : (
            <Image src={MusicOffIcon} alt="music-off-icon" className="w-4" onClick={handleMusicOn} />
          )}
          <Image src={CloseIcon} alt="close-icon" className="w-6" onClick={handleClose} />
        </div>
      </div>
    </motion.div>
  )
}

export function WinCard({
  locale = DEFAULT_LOCALE,
  reward = 0,
  width = DEFAULT_WIDTH,
  onClose,
  onStart,
  onShare,
}: CardProps) {
  return (
    <BaseCard data-title="landlord-win-card" width={width} onClose={onClose}>
      <TitleWinImage locale={locale} className="absolute -top-[120px] w-full" />
      <div className="absolute inset-x-0 bottom-8 space-y-5">
        <p
          style={{ fontFamily: '-apple-system', fontWeight: 'bold' }}
          className="text-center text-6xl leading-none text-[#FED24B]"
        >
          {formatNumber(reward)}
        </p>
        <TextWinImage locale={locale} className="mx-auto w-[240px]" />

        {/* TODO：目前沒有功能，日後才有 */}
        <div className={cn('grid gap-2', 'invisible')}>
          <BtnStart locale={locale} className="mx-auto w-[130px]" onClick={onStart} />
          <BtnShare locale={locale} className="mx-auto w-[130px]" onClick={onShare} />
        </div>
      </div>
    </BaseCard>
  )
}

export function LoseCard({ locale = DEFAULT_LOCALE, width = DEFAULT_WIDTH, onClose, onStart, onShare }: CardProps) {
  return (
    <BaseCard data-title="landlord-lose-card" width={width} onClose={onClose}>
      <TitleLoseImage locale={locale} className="absolute -top-[100px] w-full" />
      <div className="absolute inset-x-0 bottom-8">
        <TextLoseImage locale={locale} className="mx-auto w-[190px]" />

        {/* TODO：目前沒有功能，日後才有 */}
        <div className={cn('mt-10 grid gap-2', 'invisible')}>
          <BtnStart locale={locale} className="mx-auto w-[130px]" onClick={onStart} />
          <BtnShare locale={locale} className="mx-auto w-[130px]" onClick={onShare} />
        </div>
      </div>
    </BaseCard>
  )
}
