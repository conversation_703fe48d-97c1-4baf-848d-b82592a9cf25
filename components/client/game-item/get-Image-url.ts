import { S3_IMAGE_URL } from '@libs/env'

// 遊戲圖取得方式
// 供應商範例 {S3_IMAGE_URL}/classic/venue/art_electronic/en-US/art_electronic.png
// 遊戲範例 {S3_IMAGE_URL}/venetian/game/pg_elecctronic/zh-CN/48.png
export function getImageUrl(params: { isGroup: boolean; groupCode: string; code: string; lng: string }) {
  const { isGroup, groupCode, code, lng: lngParam } = params

  let lng = 'zh-CN'
  if (lngParam === 'zh-CN' || lngParam === 'zh-HK') {
    lng = 'zh-CN'
  } else {
    lng = 'en-US'
  }

  const imgUrl = isGroup
    ? `${S3_IMAGE_URL}/zz/venue/${groupCode}/${lng}/${groupCode}.png`
    : `${S3_IMAGE_URL}/zz/game/${groupCode}/${lng}/${code}.png`

  return imgUrl
}
