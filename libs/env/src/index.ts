import { isServer } from '@libs/utils'

export { RuntimeConfigScript } from './runtime'

export type RuntimeConfig = {
  S3_URL: string
  S3_IMAGE_URL: string
  API_URL: string
  API_PROXY_PATH: string
  BUSINESS_ID: string
  ACCESS_KEY: string
  SECRET_KEY: string
  PLATFORM: string
  BUILD_TIMESTAMP: string
  BOT_ID: string
  GEETEST_KEY: string
}

declare global {
  interface Window {
    __RUNTIME_CONFIG__: RuntimeConfig
  }
}

/** 後端s3環境 */
export const S3_URL = isServer ? process.env.S3_URL! : window.__RUNTIME_CONFIG__?.S3_URL

/** s3圖片路徑 */
export const S3_IMAGE_URL = isServer ? process.env.S3_IMAGE_URL! : window.__RUNTIME_CONFIG__?.S3_IMAGE_URL

/** 後端api網址 */
export const API_URL = isServer ? process.env.API_URL! : window.__RUNTIME_CONFIG__?.API_URL
export const API_PROXY_PATH = isServer ? process.env.API_PROXY_PATH! : window.__RUNTIME_CONFIG__.API_PROXY_PATH

/** 商戶ID */
export const BUSINESS_ID = isServer ? process.env.BUSINESS_ID! : window.__RUNTIME_CONFIG__?.BUSINESS_ID

/** access key */
export const ACCESS_KEY = isServer ? process.env.ACCESS_KEY! : window.__RUNTIME_CONFIG__?.ACCESS_KEY

/** secret key */
export const SECRET_KEY = isServer ? process.env.SECRET_KEY! : window.__RUNTIME_CONFIG__?.SECRET_KEY

/** platform key */
export const PLATFORM = isServer ? process.env.PLATFORM! : window.__RUNTIME_CONFIG__?.PLATFORM

/** geetest key */
export const GEETEST_KEY = isServer ? process.env.GEETEST_KEY! : window.__RUNTIME_CONFIG__?.GEETEST_KEY

/** 建置時間 */
export const BUILD_TIMESTAMP = isServer
  ? process.env.NEXT_PUBLIC_BUILD_TIMESTAMP!
  : window.__RUNTIME_CONFIG__?.BUILD_TIMESTAMP

export const BOT_ID = isServer ? process.env.BOT_ID! : window.__RUNTIME_CONFIG__?.BOT_ID

export const env: RuntimeConfig = {
  S3_URL,
  S3_IMAGE_URL,
  API_URL,
  API_PROXY_PATH: API_PROXY_PATH || '/api',
  BUSINESS_ID,
  ACCESS_KEY,
  SECRET_KEY,
  PLATFORM,
  BUILD_TIMESTAMP,
  BOT_ID,
  GEETEST_KEY,
}

// 判断商户
export function isMerchant920017() {
  return env.BUSINESS_ID === '920017'
}

export function customerHideFooterCurrency() {
  return env.BUSINESS_ID === '920020'
}
