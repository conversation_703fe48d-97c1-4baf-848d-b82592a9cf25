import { NextResponse } from 'next/server'
import type { Middleware } from '@/middleware'
import { cookies } from 'next/headers'
import { uuidV4 } from '@libs/utils'
import { isPublicPath } from '@/utils/public-path'
import { regexAllowPath, ROUTES_PARAMS_KEY, ROUTES_PARAMS_VALUE } from '@/constants/routes'

export const authMiddleware: Middleware = async (request, _, next) => {
  const pathname = request.nextUrl.pathname

  // 檢查是否已登入
  const cookieStore = await cookies()
  // 檢查device-id
  const deviceId = cookieStore.get('device-id')
  if (!deviceId) {
    cookieStore.set('device-id', uuidV4())
  }

  // 檢查是否在允許的路徑中
  const isAllow = regexAllowPath.some((p) => p.test(pathname)) || isPublicPath(pathname)

  const isSign = request.nextUrl.searchParams.has(ROUTES_PARAMS_KEY.SIGN)

  if (isAllow || isSign) {
    return next()
  }

  const token = cookieStore.get('token')

  const referer = request.headers.get('referer')
  const url = new URL(referer ?? request.url)
  const search = url.searchParams

  console.log(token, 'token')

  if (!token) {
    search.set(ROUTES_PARAMS_KEY.SIGN, ROUTES_PARAMS_VALUE.NEED_LOGIN)
    return NextResponse.redirect(url.toString())
  }

  return next()
}
