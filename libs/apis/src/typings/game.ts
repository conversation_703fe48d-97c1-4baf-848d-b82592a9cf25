import { GameCode, YesOrNo } from '@/enums/common'

// 共用
export type PageSearch = {
  pageNum?: number // 默認 1
  pageSize?: number // 默認 20
}

export type PageSearchRes<T> = {
  total: number
  pageSize: number
  list: T[]
  pageNum: number
}

export type SuccessRes = {
  isSuccess: boolean
}

export type ExtraJSON = {
  strategy?: string
  projectCode?: string
  loginUrl?: string
  type?: string
  combat_area?: string
} | null

export type Supplier = {
  supplierName: string
  supplierLogo: string
  supplierId: string | null
  supplierCode: string | null
}

export type Type = {
  code: null | string | GameCode
  linkId?: string
  name: string
  icon: string
  businessId?: number
  direct?: 'yes' | 'no'
  id?: number
  sortCode?: number
}

// 收藏遊戲 (get)
export type GetCollectGameParams = PageSearch & {
  sidebarId?: number
}
// 收藏遊戲 (post)
export type PostCollectGameParams = {
  ids?: string // 可多選，多個以逗號分隔，和groupIds擇一必填
  groupIds?: string // 可多選，多個以逗號分隔，和ids擇一必填
  isCancel?: boolean // true: 取消收藏 false: 收藏
}
// 取得收藏的遊戲
export type CollectGameResponse = PageSearchRes<SidebarGameItem>

// 近期遊戲
export type GetRecentGameParams = PageSearch & {
  sidebarId?: number
}
export type RecentGameRes = PageSearchRes<SidebarGameItem>
export type RecentGameItem = {
  code?: string
  coins: string
  groupId: number
  isCollection: boolean
  direct: string
  description: string
  isCurrentRegion: boolean
  pcThumbnail: string
  groupName: string
  maintenanceStatusCd: 'normal' | 'maintenance'
  isSupportAnonymous: null
  supplier: Supplier
  name: string
  extraJson: ExtraJSON
  livePt: number
  id: number
  mobileThumbnail: string
  isGroup: boolean
  groupCode: string
}

// 推薦遊戲
export type RecommendGameReq = PageSearch & {
  sidebarId: number
}
export type RecommendGameRes = PageSearchRes<SidebarGameItem>

// 熱門遊戲
export type HotGameSearchReq = PageSearch & {
  languageCode?: string
  orderType?: 0 | 1 // 排序方式 0:升序(default) 1:降序
  supplierIds?: string
}
export type HotGameRes = PageSearchRes<SidebarGameItem>

// 游戏类型
export type GameTypeItem = {
  code: string
  linkId: number
  name: string
  icon: string
  businessId: number
  direct?: 'yes' | 'no'
  id: number
}

export interface GameProvider {
  ipImage: null
  fundTransferInStatusCd: YesOrNo
  singleWalletModeCd: YesOrNo
  code: string
  fundTransferOutStatusCd: YesOrNo
  businessId: number
  name: string
  whiteLogo: string
  logo: string
  id: number
  sort: number
  fundTransferModeCd: YesOrNo
}

export type TabItem = {
  businessId: number
  id: number
  name: string
  code: string // 類型 code
  sortCode?: number
  icon: string // 深色icon
  whiteIcon: string // 淺色icon
  direct: 'yes' | 'no'
}

export type SidebarGameListQueryParams = {
  sidebarId: number | string
  pageNum: number
  pageSize: number
  orderType?: string | number
  languageCode?: string
  typeCd?: string
  supplierIds?: string
  groupId?: string
  tabId?: string
}
export type SidebarGameListResponse = {
  total: number
  pageSize: number
  pageNum: number
  list: SidebarGameItem[]
}

// 公共基础类型
type BaseGameItem = {
  types: UnifiedGameItemType[]
  code: string | null
  groupId: number
  direct: YesOrNo
  description: string
  isCurrentRegion: boolean
  pcThumbnail: string
  groupName: string
  maintenanceStatusCd: 'normal' | 'maintenance'
  isSupportAnonymous: number | null
  supplier: Supplier
  name: string | null
  extraJson: {
    loginUrl: string
    configFileUrl: unknown[]
  } | null
  livePt: number
  id: number | null
  mobileThumbnail: string
  isGroup: boolean
  groupCode: string
  coins: string | null
  isCollection: null | boolean
}

// 侧边栏游戏项类型
export type SidebarGameItem = BaseGameItem & {
  whiteIcon?: string
}

// 游戏列表项类型
export type GameListItem = BaseGameItem & {}

export type GroupQueryParams = {
  groupTypeCd: string // 場館類型，空值表示全部
  groupName?: string // 場館名稱 模糊查詢
}
export type GroupItem = {
  thumbnail: string
  code: string
  isDelete: number
  businessId: number
  groupTypeId: number
  direct: 'yes' | 'no'
  accuracy: null
  sort: number
  updatedById: number
  version: number
  coinId: number | null
  mobileStatusInd: 'enable' | 'disable'
  statusInd: null
  name: string
  pcStatusInd: 'enable' | 'disable'
  logo: null
  id: number
  coinName: string | 'USD' | 'CNY' | 'KRW' | 'USDT'
  createdById: number
}

export type TabGameListQueryParams = {
  pageNum: number
  pageSize: number
  tabId: string // 遊戲類型
}

export type GameListQueryParams = {
  pageNum: number
  pageSize: number
  typeCd?: string // 遊戲類型
  groupId?: string // 場館ID，空值表示全部
  name?: string // 遊戲名稱
  supplierIds?: string // 供應商ID
  orderType?: number // 排序方式 0:升序(default) 1:降序
  showDricet?: boolean // 是否只顯示直連遊戲， 1:是 其餘:否 有typo！！！
}

export type GameListResponse = {
  pageNum: number
  pageSize: number
  total: number
  list: GameListItem[]
}

export type UnifiedGameItemType = {
  code: string
  linkId: string | null // 允许 string 或 null
  name: string
  icon: string
  businessId: number | null // 允许 null
  direct: string | null // 允许 string 或 null
  id: number
  sortCode: number | null // 允许 null
  whiteIcon: string | null // 允许 string 或 null
}

export type GameLinkQueryParams = {
  language: string
  groupId: number | string
  gameId?: number | string
  coinName?: string
}
export type GameLink = {
  urlType: string // 1: game url, 2: game html
  control: string
  url: string
}

export type GameDetailParams = {
  groupId: number | string
  gameId: number | string
}
export type GameDetailResponse = {
  code: string | null
  groupId?: number
  /** 是否支持 当前区域 */
  isCurrentRegion: boolean
  livePt: number
  groupCode: string
  id?: number
  name?: string
  mobileThumbnail?: string // Mobile thumbnail image
  pcThumbnail?: string // PC thumbnail image
  isCollection?: boolean // Whether it is collected
  extraJson?: extraJson // Additional parameters
  isGroup?: boolean // Whether it is a game hall
  groupName?: string // Game hall name
  group_code?: string // Game hall code
  types?: GameInfo[]
  description?: string | null
  supplier?: Supplier // Supplier details
  direct: string // yes or no
  coins?: Coin[] // Supported coins
  /** 是否支持试玩模式 1 = supported, 2 = not supported */
  isSupportAnonymous?: 1 | 2
  maintenanceStatusCd: 'normal' | 'maintenance'
}
export type extraJson = {
  strategies?: string // 支持的策略類型
  projectCode?: string // "currency" | "hash" | "hashBattle"
  loginUrl: string | null
  configFileUrl: string[]
}
export type GameInfo = {
  code: string
  linkId: string | null
  name: string
  icon: string
  businessId: number
  direct: string | null
  id: number
  sortCode: number
  whiteIcon: string | null
}
export type Coin = {
  coinId?: string
  coinName?: string
  webLogo?: string | null
  appLogo?: string | null
}

export type HotProviderResponse = HotProvider[]
export type HotProvider = {
  name: string
  statusInd: string
  businessId: number
  thumbnail: string
  direct: string
  mobileStatusInd: string
  pcStatusInd: string
  groupTypeId: string
  code: string
  id: string
}

export type GameGroupResponse = GameGroup[]
export type GameGroup = {
  code: string
  businessId: number
  name: string
  whiteLogo: string
  logo: string
  id: number
  sort: number
  createdByTime?: string
  updatedByTime?: string
  fundTransferModeCd: 'yes' | 'no' // 是否支持轉帳
  singleWalletModeCd: 'yes' | 'no' // 是否支持單錢包
  fundTransferInStatusCd: 'yes' | 'no' // 資金轉入狀態
  fundTransferOutStatusCd: 'yes' | 'no' // 資金轉出狀態
}

export type GameRecordsQueryParams = {
  pageNum: number
  pageSize: number
  productCd?: string
  startTime: number
  endTime: number
}

export type GameRecordsResponse = {
  list: GameRecord[]
  pageNum: number
  pageSize: number
  total: number
}

export type GameRecord = {
  groupType: number
  createdByTime: number
  groupId: number
  businessId: number
  icon: string | null
  orderStatus: string
  gamePlay: string
  realAmount: number
  uid: number
  gameName: string
  odds: number
  id: number
  whiteIcon: string | null
  groupCode: string
  tableNo: string | null
  gameId: number
  amount: number
  clearingStatus: string
  orderNo: string
  nickName: string
  extendJson: string
  tricks: string
  profitLoss: number
  settDtt: number
  hideStatus: string | null
  groupName: string
  gameCode: string
  productCd: string
  periodNo: string | null
  coin: string
}
