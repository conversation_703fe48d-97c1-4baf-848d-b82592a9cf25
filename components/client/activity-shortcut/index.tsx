'use client'

import { useEffect } from 'react'

// hooks
import { useAuth } from '@modules/auth/src'
import useActivityGiftShow from '@/hooks/useActivityGiftShow'
import useActivityList from '@/hooks/useActivityList'
import { showToast } from '@/components/client/toast'
import { useTranslate } from '@tolgee/react'

// components
import { ActivityLottieMap } from '@/components/client/lottie'
import ActivityButton from './activity-button'
import GiftBox from './gift-box'
import { useLayoutStore } from '@/store/layout'
import { useActivityStore } from '@/store/activity'
import LuckyWheelModel from '@/app/[lng]/promo/(activity)/lucky-wheel/components'

// types
import type { LottieComponent, LottieItem } from '@/components/client/lottie'
import { useRouter, useSearchParams } from 'next/navigation'
import { OFFER_ACTIVITY_CODE } from '@/constants/offer-activity'

export type LottieItemWithOnNavigate = LottieItem & { onNavigate: () => void }

type Coordinate = {
  top: string
} & ({ right: string } | { left: string })

const Positions: Coordinate[] = [
  { right: '0px', top: '300px' },
  { right: '0px', top: '400px' },
  { right: '0px', top: '500px' },
  { left: '0px', top: '300px' },
  { left: '0px', top: '400px' },
]

export default function ActivityShortcut() {
  const user = useAuth()
  const { t } = useTranslate()
  const { layoutData } = useLayoutStore()
  const { data: activityData, error: activityError } = useActivityGiftShow(user.isLogin)
  const {
    error: activityListError,
    luckyWheelInfo,
    filterActivityList,
    navigateActivity,
  } = useActivityList('all', activityData)
  const { showLuckWheelId } = useActivityStore()

  const router = useRouter()
  const searchParams = useSearchParams()
  const showLuckWheel = searchParams.get('showLuckWheel')

  // 後台設定開啟的活動
  const canViewActivityList: string[] = []
  if (activityData) {
    for (const [type, isOpen] of Object.entries(activityData)) {
      // 1：表示開啟，只顯示開啟的活動
      if (isOpen !== 1) continue // ⚠️ 註解掉，全部顯示
      canViewActivityList.push(type)
    }
  }
  const lottieVersion = layoutData?.dbJson?.config?.lottieShortcutVersion ?? 'v1'
  const lottieList: LottieItemWithOnNavigate[] = canViewActivityList
    // 取得活動的 lottie 元件
    .map((type) => {
      const config = ActivityLottieMap[type]

      if (type === OFFER_ACTIVITY_CODE.LUCKY_ROULETTE) {
        const version = `v${Number(luckyWheelInfo?.wheelType || 0) + 1}` as 'v1' | 'v2' | 'v3'
        return {
          ...config,
          version,
        }
      }

      return config
    })
    // 過濾掉 undefined
    .filter((item) => !!item)
    // 綁定點擊事件
    .map((item) => ({
      ...item,
      onNavigate: () => {
        if (item.type === OFFER_ACTIVITY_CODE.LUCKY_ROULETTE) {
          navigateActivity(OFFER_ACTIVITY_CODE.LUCKY_ROULETTE, '')
          return
        }

        const activityList = filterActivityList(item.type)
        const firstActivity = activityList[0]

        if (firstActivity) {
          navigateActivity(
            firstActivity.activiteType,
            String(firstActivity.id),
            firstActivity.isLink,
            firstActivity.url,
          )
        } else {
          showToast.error(t('activity_not_found'))
        }
      },
    }))

  // 顯示錯誤
  useEffect(() => {
    if (activityError) {
      showToast.error(activityError.message)
    }
  }, [activityError])

  // 顯示錯誤
  useEffect(() => {
    if (activityListError) {
      showToast.error(activityListError.message)
    }
  }, [activityListError])

  useEffect(() => {
    if (showLuckWheel && luckyWheelInfo) {
      router.replace('/home')
      navigateActivity(OFFER_ACTIVITY_CODE.LUCKY_ROULETTE, String(luckyWheelInfo.id))
    }
  }, [showLuckWheel, luckyWheelInfo])

  // 未登入，不顯示
  if (!user.isLogin) return null

  return (
    <>
      {lottieList.length >= 2 ? (
        // 如果活動數量 >= 2，則顯示禮物盒
        <GiftBox lottieVersion={lottieVersion} lottieList={lottieList} className="right-0 top-[300px]" />
      ) : (
        // 如果活動數量只有 1 個，則顯示按鈕
        <LottieButtons lottieVersion={lottieVersion} lottieList={lottieList} />
      )}
      {showLuckWheelId && <LuckyWheelModel />}
    </>
  )
}

type LottieButtonsProps = {
  lottieVersion: 'v1' | 'v2'
  lottieList: LottieItemWithOnNavigate[]
}
function LottieButtons({ lottieVersion = 'v1', lottieList }: LottieButtonsProps) {
  const lottieButtons: React.ReactNode[] = lottieList.map((item, index) => {
    const position = Positions[index] ?? {}
    const Lottie: LottieComponent = item.lottie

    return (
      <ActivityButton
        className="absolute right-0 top-0 z-10"
        style={{ ...position }}
        key={`${item.type}`}
        onClick={item.onNavigate}
      >
        <Lottie version={item.version || lottieVersion} />
      </ActivityButton>
    )
  })

  return lottieButtons
}
