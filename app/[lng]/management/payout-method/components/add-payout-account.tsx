// Third-party imports
import { useEffect, useMemo } from 'react'
import { Modal, Form, Input, Button, App } from 'antd'
import Image from 'next/image'
import useSWR from 'swr'
import useSWRMutation from 'swr/mutation'
import { useTranslate } from '@tolgee/react'

// Local imports
import { createApi } from '@libs/apis'
import { CodeDetail, VerifyCodeType } from '@libs/typings'
import { cn } from '@libs/utils'
import CodeDict from '@/components/client/code-dicts'
import UploadImg from '@/components/client/upload-img'
import CodeDictSelector from '@/components/client/code-dicts-selector'
import SecondaryVerificationModal from '@/components/client/secodary-verification'
import { getKeyByPaymentSupportItemString } from './utils'

// Store imports
import { useCodeDictsStore } from '@/store/code-dicts'
import { useSecondaryVerificationModalStore } from '@/components/client/secodary-verification/store'
import { useUserStore } from '@/store/user'

// Type imports
import type {
  SupportedPayment,
  GetFiatPaymentLisItem,
  PaymentSupportItemString,
  GetCanPaymentListResponse,
  PaymentAccountOptions,
  GetCanPaymentListItem,
} from '@libs/apis'

type AddPayoutAccountModalProps = {
  open: boolean
  editItem?: PaymentAccountOptions
  allPaymentList?: GetCanPaymentListResponse
  onClose: () => void
  onSuccess?: () => void
}

export default function AddPayoutAccountModal({
  open,
  editItem,
  allPaymentList,
  onClose,
  onSuccess,
}: AddPayoutAccountModalProps) {
  const { t } = useTranslate()

  const [form] = Form.useForm<
    {
      currency: SupportedPayment
      method: SupportedPayment
    } & Record<PaymentSupportItemString, string>
  >()

  const { message } = App.useApp()

  const { fetchDictByType } = useCodeDictsStore()
  const { setBusinessType } = useUserStore()
  const { setOpen: openSecondaryVerificationModal, open: secondaryVerificationModalOpen } =
    useSecondaryVerificationModalStore()

  const currency = Form.useWatch('currency', form)
  const method = Form.useWatch('method', form)

  const { data: apiData } = useSWR(`AddPayoutAccountModal-${open}`, async () => {
    if (!open) {
      return null
    }
    const { data: availablePayment } = await createApi().apis.finance.getPaymentList()
    const supportedPaymentDict = await fetchDictByType(CodeDetail.SupportedPayment)

    const availablePaymentMap = availablePayment.reduce(
      (acc, item) => {
        const { statusCd, payments } = item || {}

        const isBlack = statusCd !== 'ENABLE' || payments?.length === 0
        if (isBlack) {
          return acc
        }

        return {
          ...acc,
          [item.coinSymbol]: item,
        }
      },
      {} as Record<SupportedPayment, GetFiatPaymentLisItem>,
    )

    if (editItem) {
      const currency = allPaymentList?.find((item) => String(item.id) === String(editItem.coinId))
      const paymentForms =
        availablePaymentMap?.[currency?.symbol || 'BANK']?.paymentSupportItems?.[editItem?.paymentTypeCd]

      const formFields = paymentForms?.reduce((acc, item) => {
        console.log(item)
        const formField = getKeyByPaymentSupportItemString({ key: item, data: editItem })
        return {
          ...acc,
          ...formField,
        }
      }, {})

      form.setFieldsValue({
        currency: currency?.symbol,
        method: editItem?.paymentTypeCd,
        ...formFields,
      })
    }

    return {
      availablePaymentMap,
      paymentDictMap: supportedPaymentDict?.map,
    }
  })

  const { trigger: submitDeposit, isMutating: isSubmitDepositLoading } = useSWRMutation(
    'AddPayoutAccountModal-submit',
    async (url, { arg }: { arg: { coinId: string; paymentTypeCd: string } }) => {
      if (editItem) {
        return await createApi().apis.finance.modifyPayment({
          data: {
            id: editItem.id,
            ...arg,
          },
        })
      }

      return await createApi().apis.finance.setAddPayment({
        data: arg,
      })
    },
  )

  const { availablePaymentMap, paymentDictMap } = apiData || {}
  const currencyList = useMemo(() => {
    return allPaymentList?.filter((item) => availablePaymentMap?.[item.symbol])
  }, [allPaymentList, availablePaymentMap])

  // 币种选项
  const currencyOptions = useMemo(() => {
    return currencyList?.map((item) => ({ label: item.coinName, value: item.symbol, origin: item }))
  }, [currencyList])
  // 当前币种详细数据
  const currencyItem = useMemo(() => {
    return currencyList?.find((item) => item.coinName === currency)
  }, [currencyList, currency])

  // 支付方式选项
  const paymentOptions = useMemo(() => {
    return availablePaymentMap?.[currency]?.payments?.map((item) => ({ label: item, value: item as SupportedPayment }))
  }, [availablePaymentMap, currency])

  // 当前支付方式表单列表
  const paymentForms = useMemo(() => {
    return availablePaymentMap?.[currency]?.paymentSupportItems?.[method]
  }, [availablePaymentMap, currency, method])

  useEffect(() => {
    const [first] = currencyOptions || []

    if (!currency) {
      form.setFieldsValue({
        currency: first?.value,
      })
    }
  }, [currencyOptions, currency])

  const handleFinish = async () => {
    await form.validateFields()
    // 设置二级验证类型
    setBusinessType(VerifyCodeType.WithdrawAddress)
    openSecondaryVerificationModal(true)
  }

  const handleSubmit = async () => {
    const formData = form.getFieldsValue()
    const { method, ...others } = formData

    if (!currencyItem?.id || !method) {
      message.error(t('payout_method.please_select_methods', ''))
      return
    }

    await submitDeposit({
      coinId: currencyItem?.id ?? '',
      paymentTypeCd: method ?? '',
      ...others,
    })

    message.success(Boolean(editItem) ? t('payout_method.modify_success', '') : t('payout_method.add_success', ''))

    handleCancel()
    onSuccess?.()
  }

  const handleCancel = async () => {
    form.resetFields()

    onClose()
  }

  return (
    <Modal
      title={Boolean(editItem) ? t('payout_method.modify_payout_method', {}) : t('payout_method.add_payout_method', {})}
      width={540}
      open={open}
      centered
      destroyOnClose
      onCancel={handleCancel}
      footer={
        <div className="mt-4 flex items-center justify-end gap-4">
          <Button
            className="flex-1"
            size="large"
            type="default"
            onClick={handleCancel}
            disabled={isSubmitDepositLoading}
          >
            {t('deposit.cancel', {})}
          </Button>
          <Button
            className="flex-1"
            size="large"
            type="primary"
            loading={isSubmitDepositLoading}
            onClick={handleFinish}
          >
            {t('deposit.confirm', {})}
          </Button>
        </div>
      }
    >
      <Form className="mt-4" form={form} onFinish={handleFinish} layout="vertical">
        <Form.Item
          name="currency"
          label={t('payout_method.payment_currency', {})}
          rules={[{ required: true, message: t('payout_method.payment_currency_is_required', {}) }]}
        >
          <Currency
            options={currencyOptions}
            onChange={(values) => {
              form.resetFields()
              form.setFieldsValue({
                currency: values,
                method: undefined,
              })
            }}
          />
        </Form.Item>

        {currency && (
          <Form.Item
            name="method"
            label={t('payout_method.payment_method', {})}
            rules={[{ required: true, message: t('payout_method.payment_method_is_required', {}) }]}
          >
            <Methods
              options={paymentOptions}
              onChange={(values) => {
                form.resetFields()
                form.setFieldsValue({
                  currency: currency,
                  method: values,
                })
              }}
            />
          </Form.Item>
        )}

        {method &&
          paymentForms?.map((item) => {
            return (
              <Form.Item
                key={item}
                name={item}
                label={paymentDictMap?.[item]}
                rules={[{ required: true }]}
                hidden={item === 'BANK_CODE'}
              >
                <FormItemByField field={item} name={paymentDictMap?.[item]} method={method} />
              </Form.Item>
            )
          })}
      </Form>
      {secondaryVerificationModalOpen && <SecondaryVerificationModal onSuccess={handleSubmit} />}
    </Modal>
  )
}

function Currency({
  value,
  options,
  onChange,
}: {
  value?: SupportedPayment
  options?: Array<{ label: SupportedPayment; value: SupportedPayment; origin: GetCanPaymentListItem }>
  onChange?: (value: SupportedPayment) => void
}) {
  return (
    <div className="flex flex-wrap gap-2">
      {options?.map((item) => (
        <div
          className={cn(
            'flex cursor-pointer items-center gap-2 rounded-full px-6 py-1 transition-all duration-300 hover:bg-primary-3 hover:text-on-button-primary',
            {
              'bg-primary-1 text-on-button-primary': item.value === value,
            },
          )}
          key={item.value}
          onClick={() => {
            onChange?.(item.value)
          }}
        >
          <Image src={item.origin?.appLogo || ''} alt={item.value} width={20} height={20} />
          <span>{item.value}</span>
        </div>
      ))}
    </div>
  )
}

function Methods({
  value,
  options,
  onChange,
}: {
  value?: SupportedPayment
  options?: Array<{ label: string; value: SupportedPayment }>
  onChange?: (value: SupportedPayment) => void
}) {
  return (
    <div className="grid grid-cols-4 gap-2">
      {options?.map((item) => (
        <div
          key={item.value}
          className={cn(
            'flex min-h-[48px] cursor-pointer items-center justify-center rounded-lg border-2 border-gs-600 text-sm text-bgs-900 transition-all duration-300 hover:border-primary-3 hover:text-primary-1',
            {
              'border-primary-1 bg-bgs-300 text-primary-1': item.value === value,
            },
          )}
          onClick={() => {
            onChange?.(item.value)
          }}
        >
          <CodeDict type={CodeDetail.payMethodCd} textId={item.value?.toString() || ''} />
        </div>
      ))}
    </div>
  )
}

function FormItemByField({
  field,
  name,
  value,
  onChange,
}: {
  field: PaymentSupportItemString
  method: string
  name?: string
  value?: string
  onChange?: (value: string) => void
}) {
  const { fetchDictByType } = useCodeDictsStore()
  const { t } = useTranslate()

  const form = Form.useFormInstance()
  const bankName = Form.useWatch('BANK_NAME', form)

  const { data: bankCodes } = useSWR(`AddPayoutAccountModal-${bankName}`, async () => {
    const { options } = await fetchDictByType(CodeDetail.SupportedBank)
    return options
  })

  const bankCode = useMemo(() => {
    return bankCodes?.find((item) => item.codeKey === bankName)?.codeVal
  }, [bankCodes, bankName])

  useEffect(() => {
    form.setFieldsValue({
      BANK_CODE: bankCode,
    })
  }, [bankCodes])

  if (field === 'QR_CODE') {
    return (
      <UploadImg
        value={value}
        onChange={(value) => {
          if (Array.isArray(value)) {
            onChange?.(value[0])
          } else {
            onChange?.(value)
          }
        }}
      />
    )
  }

  if (field === 'BANK_NAME') {
    return (
      <CodeDictSelector
        size="large"
        value={value}
        onChange={onChange}
        labelKey="codeKey"
        valueKey="codeKey"
        placeholder={t('deposit.please_select_bank', {})}
        codeType={CodeDetail.SupportedBank}
      />
    )
  }

  if (field === 'BANK_CODE') {
    return <Input size="large" value={bankCode} readOnly />
  }

  if (field === 'CHAIN_TYPE') {
    return (
      <CodeDictSelector
        size="large"
        value={value}
        onChange={onChange}
        codeType={CodeDetail.virtualCurrencyChain}
        placeholder={t('deposit.please_select_virtual_currency_chain', {})}
      />
    )
  }

  if (field === 'ACCOUNT_TYPE') {
    return (
      <CodeDictSelector
        size="large"
        value={value}
        onChange={onChange}
        codeType={CodeDetail.SupportedAccountType}
        placeholder={t('deposit.please_select_virtual_currency_chain', {})}
      />
    )
  }

  return (
    <Input
      size="large"
      placeholder={t('deposit.please_enter') + name}
      value={value}
      onChange={(e) => onChange?.(e.target.value)}
    />
  )
}
