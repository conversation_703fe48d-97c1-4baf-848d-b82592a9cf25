{"accessRestrictions": {"description": "Dear players:", "description2": "Your country and region is prohibited from accessing our website. If you have any questions, please contact our online customer service directly. We deeply apologize for the inconvenience caused to you!", "title": "Access Restrictions"}, "account": {"agent": "Agent", "amount": "Amount", "asset_distribution": "Asset Distribution", "asset_overview": "Asset Overview", "conversion_currency": "Conversion Currency", "currency": "<PERSON><PERSON><PERSON><PERSON>", "financial_record": "Financial Record", "frozen_assets": "<PERSON><PERSON><PERSON>", "hide_assets_less_than_1": "Hide assets ＜ 1", "liquid_assets": "Liquid Assets", "mainnet": "Network", "member": "Member", "shareholder": "Shareholder", "total_assets": "Total Assets", "unset_withdrawal_pwd": "You haven't set a \"withdrawal password\" yet. Please set it first before adding an address or receiving account.", "user_id": "User UID", "user_type": "Account Type", "vip_level": "VIP Level"}, "activity": "Activity rewards", "activity.activity_is_over": "Activity is over", "activity.can_get_rewards": "More days then can get rewards", "activity.continuous_sign_rewards": "Sign in rewards", "activity.days": "Days", "activity.fission_init": "Initial reward", "activity.fission_invitation": "Invitation", "activity.fission_sweepstakes": "Sweepstake", "activity.get_daily_reward": "Get daily reward", "activity.get_daily_sign_in_rewards": "Get daily sign-in rewards", "activity.got": "Got", "activity.got_it": "Got it", "activity.got_randomly": "Got randomly", "activity.hours": "Hours", "activity.instruction": "Instruction", "activity.mins": "<PERSON>s", "activity.not_found": "Activity not found", "activity.proceed_next_invite": "Proceed next round of invitation", "activity.rain_now": "Rain Now", "activity.red_envelopes_rain": "Red Envelopes Rain", "activity.rules": "Rules", "activity.secs": "Secs", "activity.signed_in_continuously": "Already signed in for", "activity.sign_in": "Check-in", "activity.sign_in_reminder": "Reminder", "activity.sign_in_successfully": "Sign in successfully", "activity.sign_more": "Sign in for", "activity.take_it": "Take it", "activity.time_left": "Time Left", "activity.wait_for_you": "Wait for you", "agent": {"action": "Action", "active_member_count": "Active Member Count", "active_users": "Active Users", "add": "Added successfully", "add_fail": "Addition failed", "add_new_invite_code": "Add New Invite Code", "agency_commission": "Agency Commission", "agent_invite_title": "Invite Friends & Earn <PERSON>o", "agent_invite_title1": "Enjoy the highest commissions in the industry with {BrandName}", "agent_level": "Agent Level", "agent_tier": "Agent Tier", "all": "All", "allianceCenter": "Alliance Center", "all_records": "All Records", "amount": "Amount", "amount_range": "Amount Range", "and": "And", "apply": "Apply for the pyramid agency model and get more rebates!", "apply_for_pyramid": "Apply for the pyramid agency model", "apply_now": "Apply Now", "apply_pyramid": "Apply for Pyramid Agent", "apply_pyramid_tips": "The pyramid agent model allows you to manage friend invitations and earn high transaction rebates", "appyl_pyramid_text1": "When calculating daily rebate earnings,", "appyl_pyramid_text2": "You can earn", "appyl_pyramid_text3": "An additional rebate of up to {pyramidMaxRatio}%.", "appyl_pyramid_text4": "You can freely allocate your rebate ratios", "appyl_pyramid_text5": "The ratio you keep for yourself", "appyl_pyramid_text6": "La proporción que distribuyes entre tus amigos", "appyl_pyramid_text7": "Mine", "appyl_pyramid_text8": "Friends", "appyl_pyramid_text9": "We are delighted to collaborate with like-minded and creative individuals. Become a pyramid agent and join us in promoting the values of trustworthy and secure transactions.", "area": "Regional Agent", "area_commission": "My regional commission", "area_data_overview": "Data Overview", "area_invitation_details": "Invitation Details", "area_level_ratio": "Area Level/Ratio", "area_ratio": "Regional Rebate level/Ratio", "area_rebate": "My Area Rebate", "area_rebate_details": "Rebate Details", "area_rebate_ratio": "Area Rebate Ratio", "area_rebate_tier": "My Area Rebate Tier", "area_report": "Area User Report", "area_rule": "Regional Agent Rules", "area_tip1": "・Team members: All users under your network", "area_tip2": "・Team performance: Transaction fees generated by team users", "area_tip3": "・Upgrade effective time: Update on T+1 day; takes effect before the next day’s commission payout upon meeting upgrade conditions", "area_tip4": "・Downgrade: If upgrade conditions are not met for 7 consecutive days, downgrade occurs on T+7 day, taking effect before the commission payout", "area_transaction_fee": "Rebate Base", "back_to_superior": "Back to Superior", "banner_title": "How to invite friends and share trading commissions?", "bet": "Bet", "betAmount": "Bet", "bet_card_games": "Bet-Card", "bet_live_casino": "Bet-Live Casino", "bet_lottery_games": "Bet-Lottery", "bet_ra": "Bet-Casino", "bet_slot_games": "Bet-Slot", "bet_sports_esports": "Bet-Sports", "betting": "Betting", "betting_commission": "Betting Commission", "betting_rankings": "Betting Rankings", "bill_log_statistics_report": "Bill <PERSON> Statistics Report", "black_user1": "Sorry! Our system has detected this account as abnormal.", "black_user2": "Abnormal accounts will no longer receive any commissions. If you have any questions, please contact customer support.", "bonus": "Bonus", "bonus_delivery": "Bonus Delivery", "change_code_name": "Reset Invitation Code Name", "clear": "Clear", "code_management": "Invitation Code Management", "comission_sort": "Sort", "commission_record": "Commission Record", "commission_records": "Commission Record", "commission_report": "Commission Record Report", "commission_return": "Commission Return", "commission_type": "Commission Type", "contact_info": "Contact Information", "contributor_uid": "Contributor UID", "create_time": "Created Time", "currency": "<PERSON><PERSON><PERSON><PERSON>", "dataOverview": "Data Overview", "date": "Date", "deduction": "Deduction", "default": "<PERSON><PERSON><PERSON>", "delete": "Deleted successfully", "delete_fail": "Deletion failed", "delete_invite_code": "Delete Invite Code", "delete_invite_code_tips": "Are you sure you want to delete this invite code?", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "deposit_amount": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "details": "Details", "direct_members": "Direct Members", "disclaimer": "*Special note: Due to the complexity of related business data, calculation errors and delays may occur. The above data is for reference only. We appreciate your understanding for any inconvenience.", "distribution": "Multi-level", "distribution_commission": "Multi-Level Rebates", "distribution_rule": "Distribution Agent Rule", "download": "Download", "email": "Email", "email_tips": "P<PERSON>ce enter your email", "email_tips1": "<PERSON><PERSON>ce enter correct email address", "end_time": "End Time", "export": "Export", "export_context": "Historical data is retained for a maximum of 3 months", "failed": "Submission failed", "fee_rate": "Fee Rate", "filter": "Filter", "filterApplied": "Filter conditions applied", "financial_records": "Financial Records", "financial_records_report": "Financial Records Report", "financial_statistics": "Financial Statistics", "financial_statistics_title": "Financial Statistics", "first_deposit": "First Deposit", "first_deposit_users": "First Deposit Users", "first_time_depositors": "First Time Depositors", "friend": "Friend", "friend_reg": "Friend Registration & Trading", "game_details": "Game Details", "game_items": "Game Items", "game_list": "Game List", "game_record": "Game Record", "game_records": "Game Records", "game_records_report": "Game Records Report", "game_statistics": "Game Statistics", "game_win_or_lose": "Game Win or Lose", "got_it": "Got it", "invitation_code": "Invitation Code", "invitationDetails": "Invitation Details", "invitation_link": "Invitation Link", "invitationRebateDetails": "Your invitation and rebate details", "invite_code_name": "Invite Code Name", "invite_code_name_tip": "Please enter the invite code name", "invite_code_name_tip1": "Up to 20 characters, a combination of letters and numbers", "invite_friend": "Invite Friends", "invite_friends": "Invite Friends", "inviter": "Inviters", "level": "Level", "level1TransactionFee": "Level 1 Transaction Fee", "level2TransactionFee": "Level 2 Transaction Fee", "level3TransactionFee": "Level 3 Transaction Fee", "levelShort1": "LV1:", "levelShort2": "LV2:", "levelShort3": "LV3:", "management": "Management", "management_fees": "Management Fees", "max": "Maximum", "me": "Me", "member_balance": "Member Balance", "member_bonus": "Member Bonus", "member_bonuses": "Member Bonuses", "member_cashback": "Member <PERSON><PERSON>", "member_deposit": "Member De<PERSON><PERSON><PERSON>", "member_rebate": "Member Rebate", "membership_fees": "Membership Fees", "member_withdrawal": "Member <PERSON><PERSON><PERSON>", "min": "Minimum", "mobile": "Mobile", "more_user_details": "More user details", "my_distribution_commission": "My Multi-level Rebate", "myRebate": "My Rebate", "my_referral_details": "My Referral Details", "myTier3Rebate": "My Tier-3 Rebate", "myTier3RebateLadder": "My Tier-3 Rebate Ladder", "national": "Universal Agent", "net_deposits": "Net Deposits", "net_win_or_lose": "Net Win or Lose", "new_first_deposits": "First Deposits", "new_members": "New Members", "new_registrations": "New Registrations", "new_users": "New Users", "no_export_data": "No Export Data", "no_remind_today": "Do not remind me again today", "number_bets": "Number of Bets", "number_of_invites": "<PERSON><PERSON><PERSON>", "or": "Or", "order_number": "Order Number", "other": "Other", "other_members": "Other Members", "other_team_data": "Other Team Data", "pagination_total": "Total: {total}", "pending": "Under Review", "pending_reason": "Your application has been successfully submitted. We will complete the review within 72 hours and notify you via email or other means.", "pending_title": "Application Submitted", "person": "Person", "phone_number": "Phone Number", "phone_number_tips": "Please input your phone number!", "platform": "Platform", "product_cd": "Product Code", "product_line": "Production Line", "product_line_type": "Product Line Type", "product_type": "Product Type", "profit_and_loss": "Profit and Loss", "profit_and_loss_all": "Profit And Loss", "profit_loss": "Profit Loss", "profit_loss_commission": "Profit Loss", "pyramid": "Pyramid Agent", "pyramid_apply1": "Apply now for the pyramid agent model to earn more trading rebates", "pyramid_apply10": "Receive detailed financial reports about your invited friends", "pyramid_apply2": "{<PERSON><PERSON><PERSON>} will review your application. Once approved, you will gain the rights to invite and manage friends using an agent invitation code/link.", "pyramid_apply3": "Set your commission-sharing ratio for your agent code/link, and share it with friends to earn crypto together.", "pyramid_apply4": "Pyramid Agent Model", "pyramid_apply5": "The pyramid agent model allows you to manage invitations and earn high trading rebates", "pyramid_apply6": "Manage your agent invitation codes and freely assign commission ratios", "pyramid_apply7": "High Commission Rewards", "pyramid_apply8": "Earn high commission rewards", "pyramid_apply9": "Transparency", "pyramid_commission": "Pyramid Commission", "pyramid_commission1": "Please apply for the pyramid agent model first", "pyramid_commission_ratio": "Pyramid Commission Ratio", "pyramid_data_overview": "Data Overview", "pyramid_invitation_details": "Invitation Details", "pyramid_ratio": "Pyramid Ratio", "pyramid_rebate": "My Pyramid Rebate", "pyramid_rebate_details": "Rebate Details", "pyramid_report": "Pyramid User Report", "pyramid_rule": "Pyramid Agent Rules", "pyramid_transaction_fee": "Rebate Base", "reason": "Reason", "rebate": "Rebate", "rebate_details": "Rebate Details", "rebateDetails": "Commission Details", "rebate_ratio": "Rebate Ratio", "rebate_rules": "Rebate Rules", "rebate_time": "Rebate Time", "rebate_type": "Rebate Type", "regional_commission": "Regoinal Commission", "regional_level_ratio": "Regional Level/Ratio", "regional_rebate_ratio": "Regional Rebate Ratio", "registration_time": "Registration Time", "reject": "Rejected", "reject_title": "Review Rejected", "reset": "Reset", "review_failed": "Review Failed", "save": "Save", "search_button": "Search", "search_suprior_uid": "Search Suprior UID", "search_user": "Search User", "search_user_uid": "Search User UID", "set_failed": "Set Failed", "set_pyramid": "Set Pyramid Commission Ratio", "set_pyramid1": "Changing the invitation code ratio applies only to newly invited users; existing users remain unaffected.", "set_pyramid2": "Only supports increasing the ratio", "set_success": "Set Success", "setting": "Setting successful", "setting_default": "Setting Default", "setting_default_code": "Set Default Invitation Code", "setting_fail": "Setting failed", "settled_bets": "Settled Bets", "settlement_currency_amount": "Settlement Currency Amount", "share_commission": "Share Commission", "size_overview": "Size Statistics", "social_media": "Social Media", "social_media_tip": "Please enter your social media account", "start_time": "Start Time", "submitted_application": "You have successfully submitted your application", "subordinate_data": "Subordinate Data", "subordinate_uid": "Subordinate UID", "success": "Submission successful", "superior_uid": "Superior UID", "supplymentry": "Supplementary application description (optional)", "team_contri": "Team Contribution", "team_details": "Team Details", "team_members": "Team Size", "team_new_wl": "Team New Win/Loss", "team_num": "Team Size", "team_recharge": "Team Recharge", "team_size": "Team Size", "teamSize": "Team Size", "team_statistics": "Team Statistics", "team_total_bets": "Team Total Bets", "team_withdrawal": "Team Withdrawal", "their_invitation": "Their Invitation", "their_number_of_invitees": "Their Number of Invitees", "their_number_of_invites": "Their Number of Invites", "their_number_of_team_members": "Their Number of Team Members", "three_level": "Tier-3 Agent", "three_level_report": "Three Level User Report", "three_level_rule": "Three-Level Agent Rules", "three_tip1": "・Successful invites: Direct subordinate users invited through my referral code", "three_tip2": "・Direct referral performance: Transaction commission generated by my direct subordinates", "three_tip3": "・Upgrade effective time: Update on T+1 day; takes effect before the next day’s commission payout upon meeting upgrade conditions", "three_tip4": "・Downgrade: If upgrade conditions are not met for 7 consecutive days, downgrade occurs on T+7 day, taking effect before the commission payout", "tier_1": "Tier-1", "tier_2": "Tier-2", "tier_3": "Tier-3", "tier_3_commission": "My tier-3 commission", "tier_l_1": "LV1 Rate", "tier_l_2": "LV2 Rate", "tier_l_3": "LV3 Rate", "time_period": "Time Period", "time_range": "Time Range", "total_allocate": "Total allocable", "total_deposits": "Total Deposits", "total_team_bet": "Total Team Bet", "total_team_size": "Total Team Size", "total_withdraws": "Total Withdrawals", "transaction_fee": "Transaction Fee", "transaction_fees": "Transaction Fees", "transaction_time": "Times", "transaction_type": "Transaction Type", "type": "Type", "uid": "UID", "universal_agent_guidelines": "Universal Agent guidelines", "user": "User", "user_name": "User Name", "valid_bet": "Valid Bet", "valid_bets": "<PERSON><PERSON>s", "venue_fees": "<PERSON><PERSON><PERSON>", "warning": "Warning", "win_loss_rebate_statistics": "Win Loss Rebate Statistics", "win_or_lose": "Win or Lose", "withdrawal": "<PERSON><PERSON><PERSON>"}, "amount": {"cashback": "Cashback"}, "app_download": {"android_download": "Android download", "ios_download": "IOS download", "on_your_browser": "On your browser:", "pwa_desc": "To Install the app, you need to add this website to your home screen.", "pwa_guide": "Guide", "pwa_guide_bottom": "Please click <icon></icon> to add to the home screen", "pwa_guide_list_1": "Please click <icon></icon> Share", "pwa_guide_list_2": "Click <icon></icon> Add to Home Screen", "pwa_guide_list_3": "Open {name} on your Home Screen", "scan_download": "Scan QR code to Download"}, "authForm": {"login": "LOGIN", "register": "REGISTER"}, "character": {"bet_task": "Total effective deposit {num} ", "daily": "Daily", "get_all_word": "Receive All", "get_new_amount": "You've received <param>{num}</param> .", "get_new_count": "You've received <param>{num}(N)</param> word draws.", "get_word": "Get Word", "my_amount": "My distributed amount", "per_set": "Reward per set", "prize_draw_time": "Prize draw time：{date}", "recharge_task": "Total deposit {num} ", "remain_times": "Remain times：{num}", "set": "Set", "sets_collected": "<param>{num}</param> sets collected", "share_the_amount": "Share the Amount", "task_title": "Complete the task to get the word", "total_amount": "Total distributed amount"}, "common": {"all": "All", "cancel": "Cancel", "confirm": "Confirm", "copy_fail": "<PERSON><PERSON> Failed", "copy_success": "<PERSON><PERSON> Successfully", "days": {"fourteen_days": "Last 14 days", "seven_days": "Last 7 Days", "thirty_days": "Last 30 Days", "to_date": "To Date", "today": "Today", "yesterday": "Yesterday"}, "deposit_withdraw": "<PERSON><PERSON><PERSON><PERSON>", "empty": "No data found", "isplaying": "Playing...", "month": "Month", "next_step": "Next step", "no_new_mission": "No new mission information yet", "or": "Or", "promotions": "Offers", "reminder": "hint", "remove_self_restrictions": "Please remove your self-restrictions first", "reward_amount": "<PERSON><PERSON> Amount", "save": "Save", "setup": "Set Up", "submit": "Submit", "time": "Time", "try_user_no_deposit_withdraw": "Trial account cannot be recharged", "type": "Type", "verify_withdraw_address_fail": "Verify withdraw address fail", "wallet": "Wallet", "year": "Year"}, "common_required": "Please enter {name}", "cookie": {"accept": "Accept", "link": "Cookies Policy", "message": "This website uses cookies to ensure you get the best experience. By continuing to browse, you agree to our", "reject": "Reject"}, "demo_web": {"iLoveGame_des": "Permanent home: 52rs7.cc 52Love.cc 52LoveForum.cc", "iLoveGame_title": "Exclusive PG Demo Simulator by 52Love Forum"}, "deposit": {"account_name": "Account", "action": "Action", "all_deposit_records": "All Deposit Record", "amount": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "amount_limit": "Limit: {min}-{max}", "asset_overview": "Asset Overview", "assets": "Assets", "bonus": "Bonus", "cancel": "Cancel", "change_value": "Change value", "completion_time": "Completion time", "confirm": "Confirm", "contract_address": "Contract Address", "copy": "Copy", "copy_successfully": "<PERSON><PERSON> Successfully", "creation_time": "Creation time", "crypto": "Crypto", "cryptocurrency": "Deposit Cryptocurrency", "currency": "Crypto", "currency_network_selection": "Crypto & Network Selection", "currency_selection": "Crypto Selection", "deposit_address": "Deposit Address", "deposit_amount": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "deposit_confirm_num": "Recharge requires confirmation", "deposit_details": "De<PERSON>sit Details", "deposit_records": "{name} Deposit Records", "details": "Details", "enter_amount": "Please enter the deposit amount", "error": "Error", "failed": "Failed", "fiat": "Fiat", "limit": "Limit <PERSON> ", "max_amount": "Maximum {max}", "merchant_number": "Merchant Number", "method": "Select payment method", "min_amount": "Minimum {min}", "minimum_deposit_amount": "Minimum recharge amount", "modal_title": "Fiat recharge", "no_withdraw_method": "Withdraw Method Is Empty", "only_deposit": "You Can Only Deposit {currency} to this Address", "only_deposits_of_usdt": "You Can Only Deposit USDT to this Address Deposit Other Assets Will Not Be Recovered", "other_assets": "Deposit Other Assets Will Not Be Recovered\n", "payment_currency": "Payment Currency", "pending": "Pending", "please_another_network": "Please select another Network", "please_ensure_that_you": " Please ensure that you have selected a network that is consistent with the withdrawal platform when depositing, otherwise it may result in asset loss.", "please_enter": "Please enter", "please_select_bank": "Please select bank", "please_select_virtual_currency_chain": "please select virtual currency chain", "please_transfer_network": "Please select the transfer network", "popular_currencies": "Popular Currencies", "qr_code": "Qr code", "receiver_info": "Receiver", "record_details": "Record Details", "search_currency": "Search currency", "search_history": "Search History", "select_channel": "Select Channel", "status": "Status", "submit_success": "Deposit order submitted successfully, please complete the deposit as soon as possible", "success": "Success", "thousand_amount": "{amount} x 10 Thousand", "times": "Times", "title": "<PERSON><PERSON><PERSON><PERSON>", "trade_area": "Trading zone", "transaction_id": "Transaction ID", "type": "Type", "view_history": "View history", "withdraw_confirm_num": "<PERSON><PERSON><PERSON> waiting confirmation number", "your_balance": "Your Balance"}, "deposit_and_withdrawal": "Account <PERSON><PERSON> and <PERSON><PERSON><PERSON>", "domain": {"download_app": "Download the App", "find_through_desktop": "1. Find us via the URL on the desktop icon", "remember_domains": "2. Or remember the following domain name", "reminder": "What should I do if the URL is invalid ?", "save_image": "Save image"}, "event": {"content": "Event Content"}, "features_entertainment_area_quota_conversion_index_ueleprpp22": "Under maintenance", "features_home_component_disable_game_index_2vv8jhk4ek": "Not available in your region", "fiat_currency_order_history": {"status": {"finished": "Completed", "ing": "Ongoing"}}, "finance_record": {"affiliate_mode": "Agent Model", "betting": "Betting", "creation_time": "Creation time", "deposit_withdrawal": "Depisit and withdrawals", "others": "Others", "rebate": "Rebate", "status": "Status", "time": "Time", "transaction_fee": "Transaction Fee"}, "footer": {"about_us": "About Us", "agent": "Agent", "casino": "Casino", "concat_us": "Contact Us", "cookie_policy": "Cookies Policy", "game": "Games", "games": "Games", "invite": "Invite", "oneline_support": "Online Support", "online_support": "Online Support", "promo": "Offer", "rebate": "Rebate", "shareholder": "Shareholder", "support": "Support", "support_currency": "Support Currency", "tasks": "Tasks", "user_agreement": "user agreement", "vip": "VIP"}, "games": {"group": "Venue", "not_available_region": "Not available in your region", "playing": "Playing"}, "header": {"agencyCenter": "Agency Center", "assets": "Asset Overview", "betting": "Betting Records", "inviteRebate": "Refer & Earn", "preferenceSettings": "Preference Settings", "promotions": "Offer", "security": "Account Security", "turnover": "Bonus Unlock", "vip": "VIP"}, "homePage": {"allGame": "Total", "arcade": "Arcade", "cock": "<PERSON><PERSON>", "esports": "Esports", "fish": "Fish", "gameProvider": "Game Provider", "hash": "Hash", "hot": "Popular", "lottery": "Lottery", "poker": "Poker", "slots": "Slots", "sports": "Sports"}, "language": "Language", "limit": {"edit_name_modal_key0": "Supports JPG and PNG formats, up to 10M", "edit_name_modal_key1": "Upload Pictures", "edit_name_modal_key2": "Image size not more than 10MB", "edit_name_modal_title": "Change avatar"}, "lobby": "Lobby", "maintenance": "This website is currently under maintenance! Please wait!", "mini_game": {"back": "Back", "cancel_collection": "Remove the Fovirte", "cancel_coll_game": "Successfully canceled collection", "cancel_coll_game_failed": "Failed to cancel collection", "collection": "Add a Fovirte", "coll_game": "Successfully collected", "coll_game_failed": "Failed to collect", "favorite": "No favorites yet. Use ☆ to add games to your favorites.", "full_screen": "Full Screen", "game_currency": "Your balance converted currency", "game_error": "Error loading game link", "game_provider": "Game Provider", "key_points": "Key points Introduction", "link": "Link", "load_more": "Load More", "no_login": "You are not logged in yet", "real_mode": "Real Money Mode", "recom_game": "Popular Recommendations", "reg_login": "Login/Register", "share": "Share", "show_game_items": "Showing {remaining} out of {total} games", "support_currency": "Game Supported Currencies", "theater": "Theater Mode", "try_mode": "Try Mode", "warning_tips": "Please switch the deposit currency or try demo mode", "warning_title": "The current deposit currency does not support the game currency"}, "mini_game_back": "Back", "mini_game_cancel_coll_game": "Successfully canceled collection", "myRebate": {"bet_amount": "Bet Amount", "date": "Date", "end_time": "End Time", "game_name": "Game Name", "rebate_amount": "Rebate Amount", "rebate_commission": "Rebate Bonus", "results": "Results", "start_time": "Start Time", "to_date": "To date", "transfer": "Transfer", "valid_bets": "<PERSON><PERSON>s"}, "notify": {"all_read": "All Read", "announcement": "Announcement", "announcement_title": "Announcement Title", "customer_service": "Customer Service", "customer_service_center": "Message Center", "delete": "Delete", "delete_read": "Delete Read", "got_it": "Got it", "marquee": "Marquee", "marquee_title": "Marquee Title", "notification": "Notification", "notification_title": "Notification Title", "notify_drawer_key0": "7X24 Online Customer Service", "notify_drawer_key1": "Professional customer service online dialogue to help you solve the problems you encounter.", "title": "Message Center"}, "offer": {"activity_ended": "Activity ended", "activity_not_found": "Activity not found", "all": "All", "all_games": "All Games", "already_earned": "Has reached the accumulated amount and got", "already_participated": "Already participated", "amount": "Cumulative Amount", "applied": "Applied", "apply_all": "Apply All", "apply_failed_01": "Apply Failed", "apply_success": "Apply Success", "available": "Available", "back": "Back", "bet": "Bet", "bet_last_month_ranking": "Last month's betting ranking", "bet_last_week_ranking": "Last week's betting ranking", "bet_monthly_betting_ranking": "Monthly betting ranking", "betting": "Betting", "bet_today_betting_ranking": "Today's betting rankings", "bet_weekly_betting_ranking": "Weekly betting ranking", "bet_yesterday_ranking": "Yesterday's betting ranking", "binding_successful": "Binding successful", "binding_successful_info_1": "Tap", "binding_successful_info_2": "Claim {amount} Bonus", "binding_successful_info_3": "To Start The Event.", "bonus": "Bonus", "bonus_to_claim": "Bonus to claim", "check_in": "Check In", "check_record": "Check Record", "claim": "<PERSON><PERSON><PERSON>", "Claim": "<PERSON><PERSON><PERSON>", "claim_all": "Claim All", "claim_amount_bonus": "Claim {amount} Bonus !", "claim_bonus": "Claim Bonus", "claimed": "Claimed", "Claimed": "Claimed", "claimed_success": "Success Claimed", "claim_history": "Claim History", "claim_limit_1": "Daily claim limit", "claim_limit_2": "Weekly Claim Limit", "claim_limit_3": "Monthly claim limit", "claim_limit_daily": "Daily Claim Limit", "claim_limit_monthly": "Monthly Claim Limit", "claim_limit_weekly": "Weekly Claim Limit", "coming_soon": "Coming soon!", "confirm": "Confirm", "congrats_on_getting": "Congratulation on getting {award}{symbol}", "coupon_title": "Please enter the discount code", "current_active": "Current active", "current_level": "Current Level", "currently_claimed_amount": "Currently claimed: {amount}{coin}", "current_rank_bet": "Cumulative Recharge", "current_rank_beted": "Total Bets", "current_ranking": "Current ranking", "current_rank_saved": "Single Recharge", "current_total_bet": "Current Total Bet", "current_total_bet_amount": "Current Total bet amount", "current_total_loss": "Current Total Loss", "current_total_saved": "Current Single Recharges", "current_total_turnove": "Current Total Bets", "daily_amount": "Daily Amount", "daily_bonus": "Daily Bonus", "daily_withdrawals": "Daily Withdrawals", "date": "Date", "day": "Day", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "details": "Details", "dispatched": "Dispatched", "emergency_activity": "Emergency Activity", "end_activity": "Ended", "enter_code_to_claim_amount": "Enter the code to claim ${amount}", "enter_mobile_number": "Enter mobile number", "event": "Event", "event_content": "Event Content", "event_started": "The event has started!", "event_vip": "VIP", "exchange_success": "Exchange Successful", "expected_bonus": "Expected bonus", "expired": "Expired", "Expired": "Expired", "expired_after": "Expired after {hours} hours", "expired_after_minutes": "Expired after {minutes} minutes", "expired_after_seconds": "Expired after {second} seconds", "failed": "Failed Claimed", "first_day": "First day", "fission_activity": "Fission Activity", "fission_current_round": "Current round ({round})", "fission-init": "Initial reward", "fission_initial_description": "Still need {amount}{symbol} withdraw", "fission-invitation": "Invitation", "fission_round_select": "Round {round}", "fission-sweepstakes": "Sweepstake", "free_withdrawals": "Free Withdrawals", "get_fission_initial": "Congratulations on getting {amount}{symbol}", "getting_invitation_reward": "Congratulations on getting the invitation reward", "go_now": "Go Now", "got_it": "Got it", "grand_prize": "Grand prize", "invitation_num_registration": "Invitation number registration", "invite_friends": "Invite friends", "join_label": "Select participation amount ", "join_now": "Join Now", "keep_going": "Keep Exchanging", "last_month_ranking": "Last month's deposit ranking", "last_refresh_time": "Last refresh time", "last_week_ranking": "Last week's deposit ranking", "level": "Level", "monthly_betting_ranking": "Monthly deposit ranking", "monthly_bonus": "Monthly Bonus", "need_agent": "Please apply agent before sharing.", "next_level": "To Next Level", "no_limit": "No Limit", "not_available_yet": "Not available yet", "not_eligible_for_this_event": "You are not eligible for this event.", "not_ranked": "Not ranked", "not_receive": "Not received", "now_Claim": " Collection", "offer": "Offer", "offer_details": "Offer Details", "offer_pending": "Pending", "offer_rejected": "Rejected", "open": "Open", "Overdue": "Overdue", "person": "{num}/person", "phone_number_successfully_linked": "Phone number successfully linked.", "phone_vip_invite_info": "Bind and verify your mobile number, log in daily to receive {dailyAmount}{coin} bonus, totaling {totalAmount}{coin}.", "Proceed": "Proceed", "proceed_next_rewards": "Proceed next round of rewards", "promotion_bonus": "Promotion Bonus", "random_amount": "Random Amount", "rank_desc": "Activity content: \n Exclusive for the first recharge of the account, only one chance, the more recharge, the more rewards, the highest reward is 20.00\nAfter the recharge is successful, the reward is expected to be updated in 10 minutes, please wait for the reward to be issued\nThe bonus given in this activity (excluding the principal) requires 1.5 times the effective bet to withdraw\nThis activity is limited to normal manual operations by the account itself, and it is prohibited to rent, use plug-ins, robots, different accounts to gamble, mutual brushing, arbitrage, interfaces, protocols, exploit loopholes, group control or other technical means to participate, otherwise the reward will be cancelled or deducted, frozen, or even blacklisted;\nIn order to avoid differences in text understanding, the platform will reserve the final right of interpretation of this activity.", "ranking": "Ranking", "ranking_bonus": "Ranking bonus", "rebate_activity": "Rebate Activity", "rebate_expired_days": "Expired in {day} days", "rebate_time_over_tips": "The event is over. Please claim your rewards as soon as possible.", "receive_all": "Receive All", "receive_alls_01": "Receive All", "recive_reward_failed": "Recive reward failed", "recive_reward_success": "Recive reward success", "Red Envelope Rain": "Red Envelope Rain", "refresh_rank": "Refresh rank", "registration_number": "Registration number", "rejected": "Rejected", "remaining": "Remaining", "reset_after": "Reset after", "reward_credit_automatically": "The rewards will be automatically credited to your account after system review.", "reward_received_successfully": "<PERSON><PERSON> received successfully", "rule": "Rule", "send_code": "Send code", "sign_in_activity": "Sign in activity", "sign_in_error": "Sign in error, please try again.", "sms_send_success": "SMS sent successfully. <PERSON>send in 60 seconds.", "start_event": "Start Event", "success_claimed": "Successfully Claimed", "success_claimed_day_max": "The daily salary limit of {salary} has been reached. Please come back tomorrow to claim.", "success_claimed_Monthly_max": "The monthly salary limit {salary} has been reached. Please come back next month to claim.", "success_claimed_Salary": "Successfully received {salary}", "success_claimed_week_max": "The weekly salary limit {salary} has been reached. Please come back next week to claim.", "tasks": "Tasks", "thanks_participate": "Thanks for participating", "the": "The", "the_day": "The {number} day", "The_final_secret_jackpot": "The final secret jackpot", "time_left": "Time left", "tip": "Exchange successful, congratulations!", "to_claim": "<PERSON><PERSON><PERSON>", "today_betting_ranking": "Today's betting rankings", "to_next_level": "To Next Level", "total_bet": "Total bet", "total_bets": "Total Bets", "total_bouns": "Total Bouns", "total_deposit": "Total Deposit", "total_loss": "Loss", "treasure_chest": "Treasure chest", "type": "Type", "Unclaimed": "Unclaimed", "unreach": "Unreach", "upgrade_condition": "Upgrade Condition", "user_join_res": "Participation successful, congratulations on your reward", "verification_code": "Verification Code", "verifying": "Verifying", "view": "View", "vip_power": "Vip Power", "vip_promotion": "VIP Promotion", "vip_rule_content1": "To be promoted to the next level, additional deposits and wagers must be made on top of the existing accumulated amounts.", "vip_rule_content2": "For example:", "vip_rule_content3": "To reach VIP1, a deposit of $1,000 and wagers of $2,000 are required.", "vip_rule_content4": "To reach VIP2, another deposit of $1,000 and wagers of $2,000 are required.", "vip_rule_content5": "This means a member must accumulate a total deposit and wager amount of $3,000 ($1,000 + $2,000) to be promoted to VIP2, and so on.", "weekly_betting_ranking": "This week's deposit ranking", "weekly_bonus": "Weekly Bonus", "wheel_time": "獲得轉盤券{time}次！", "win-record": "Winning Record", "yesterday_ranking": "Yesterday's ranking", "you_won": "You won"}, "open": "Open", "order": {"constants": {"status": {"settled": "Settled", "unsettled": "Unsettled"}, "won": "Won"}, "details_ongoing": "Paste the transaction ID in your payment notes to quickly claim transaction record.", "game_name": "Game Name", "game_play": "Game Play", "order_amount": "Order Amount", "order_create_time": "Order Time", "order_no": "Order Number", "profit_loss": "Profit and Loss", "real_amount": "Effective Amount", "search_button": "Search", "settlement_status": "Settlement Status", "settlement_time": "Settlement Time"}, "order_details": {"amount": "Amount", "completion_time": "Completion Time", "creation_time": "Creation Time", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "deposit_fail_1": "Your deposit order of ", "deposit_fail_2": " has been canceled, and the funds have been returned to your personal account or wallet. If you have any questions, please contact customer service.", "deposit_fiat_currency": "Fiat Deposit", "deposit_ing_1": "Paste the transaction ID in your payment notes to quickly claim transaction record.", "deposit_success_1": "You have successfully deposit ", "deposit_success_2": ". Please check the account for the amount credited.", "detail_id": "Order ID", "detail_title": "Order Details", "direction": "Direction", "fiat_withdrawal": "Fiat Withdrawal", "ing": "Processing", "payment_method": "Payment Method", "receiving_account": "Receiving Account", "settlement_time": "Settlement Time", "status": "Status", "success": "Success", "trading_zone_currency": "Trading Zone Currency", "transaction_id": "Transaction ID", "withdraw": "<PERSON><PERSON><PERSON>", "withdraw_fail_1": "Your withdrawal order of ", "withdraw_fail_2": " has been canceled, and the funds have been returned to your account. If you have any questions, please contact customer service.", "withdraw_fiat_currency": "Fiat Withdrawal", "withdraw_success_1": "You have successfully withdrawn ", "withdraw_success_2": ". Please check the your personal account or wallet for the amount credited."}, "payout_method": {"account": "Account", "account_details": "Account Detail", "actions": "Actions", "add_payout_method": "Add Payout Method", "add_payout_method2": "Add Account", "add_success": "New payment method added successfully!", "alert": "Please ensure your account is verified under your real name. Receiving payments with a non-verified account may result in order failure and account suspension.", "coin_method": "Method", "delete": "Delete", "delete_payout_account": "Confirm to unbind?", "delete_payout_account_error": "Failed to unbind, please contact the administrator.", "delete_payout_account_success": "{bank} account has been unlinked!", "delete_payout_account_tips": "After unbinding the {bank} account, you will not be able to receive payments with this account. Please confirm whether to unbind it.", "disabled": "Disabled", "edit": "Edit", "empty_payout_method": "No Receiving Account Yet", "enabled": "Enabled", "modify_payout_method": "Modify Payout Method", "modify_success": "The payment method has been modified successfully!", "payment_currency": "Payment Currency", "payment_currency_is_required": "Payment currency is required", "payment_method": "Payment Method", "payment_method_is_required": "Payment method is required", "please_select_currency": "Please select the currency you want to convert to.", "please_select_methods": "Please select currency and payment method", "please_select_payment_method": "Please select the payment method.", "title": "Payout Method Management"}, "personal_center": {"access_history": "Access History", "account_security": "Account Security", "account_security_description": "Set up account security verification methods to ensure account security.", "address": "Address", "advanced_settings": "Advanced Settings", "already_set": "Already Set", "and": " and ", "answer": "Answer", "answer_tips": "Once set successfully, it cannot be viewed again. Please save the information carefully.", "anti_phishing_code": "Anti-phishing code", "anti_phishing_code_alert": "Do not disclose the anti-phishing code to anyone, including platform staff", "anti_phishing_code_content": "The email sent to you will include your set anti-phishing code.", "anti_phishing_code_error": "Anti-Phishing Code Length Is 4-10 Digits", "anti_phishing_code_key": "Once your anti-phishing code is set, every ", "anti_phishing_code_key1": " email you receive will include it. Emails with an incorrect code are phishing attempts.", "anti_phishing_code_placeholder": "Please enter anti-phishing code", "anti_phishing_code_tip": "After The Setting Is Successful, It Will Not Be Viewable. Please Be CarefulTo Save The Information.", "attention": "Attention", "attention_text": "Please confirm whether your address and ID photo are correct. Once submitted, changes will no longer be possible", "bind_email": "Bind <PERSON><PERSON>", "bind_google_verification_code": "Bind Google Verification Code", "bind_google_verification_code_placeholder": "Please enter the 6-Digit Verification Code From your Google Authentie", "bind_phone": "Bind Phone", "bind_successful": "Binding Successful", "browser_info": "Browser Info", "change_login_password": "Change Login Password", "confidentiality_answer": "Confidentiality answer format error", "confim_password": "Confim password", "confirm_password": "Confirm password", "converted_currency": "Converted currency", "copy_key": "Copy Key", "current_login_password": "Current login password", "date": "Date", "delete_account": "Delete account", "delete_account_content": "Please note that once the account is deleted, it cannot be recovered. Once deleted, the account will no longer be accessible, and transaction history will not be viewable.", "delete_account_question": "Thank you for choosing {name}. Please select the reason for deletion to help us better improve the experience.", "delete_account_question1": "No longer want to use this account", "delete_account_question2": "Merge multiple accounts", "delete_account_question3": "Other", "delete_apply": "Account deletion request has been submitted", "email_verification": "Email Verification", "email_verification_content": "You can link a commonly used email for login, password recovery, and confirmation when withdrawing funds.", "enable_verification": "Enable Verification", "enter_answer": "Please enter the answer", "feature_settings": "Feature Settings", "google_verification": "Google Verification", "google_verification_code": "Google verification Code", "google_verification_content": "Enter the verification code from your authentication app when logging in, withdrawing funds, or changing security settings.", "got_it": "Got it", "identity_verification": "Identity Verification", "id_number": "ID number", "id_photo": "ID photo", "image_processing_error": "Error processing image", "in_self_limitation": "In self-limitation", "keep_login_duration": "Keep Login Duration", "least_2_bind": "At Least 2 Security Verifications Are Required To Be Enabled", "least_bind": "At Least {count} Security Verifications Are Required To Be Enabled", "link_account": "Link account", "linked": "Linked", "link_now": "Link now", "link_phone": "Link Phone", "log_in_again": "Log in again", "log_in_again_1": "Please log in again", "log_in_again_2": "Successfully updated, please log in again", "login_location": "Login Location", "login_log": "<PERSON><PERSON>g", "login_name_content": "Setting your login name allows you to quickly log into your account. The current login name is: {name}", "login_password": "Login Password", "login_password_content": "By setting a login password, you will be able to log in directly using your account and password.", "manage": "Manage", "manage_account": "Manage account", "manage_withdrawal_management": "Manage your withdrawal address.", "modify": "Modify", "modify_successful": "Modified Successfully", "modify_withdrawal_password": "Modify Withdrawal Password", "modify_withdrawal_password_success": "Successfully modified withdrawal password", "new_email_address": "New Email Address", "new_google_key": "New Google Key", "new_withdrawal_password": "Please enter your new withdrawal password", "not_linked": "Not linked", "not_set": "Not set", "original_login_password": "Original Password Must Be Filled In", "original_withdrawal_password": "Please enter your original withdrawal password", "payout_method": "Payout method management", "phone_number": "Phone number", "phone_verification": "Mobile verification", "phone_verification_content": "Used for receiving SMS when logging in, withdrawing funds, changing passwords, and adjusting security settings.", "Please_converted_currency": "Please select the currency you want to convert to.", "please_select": "Please select", "release": "Release", "request": "Request", "reset": "Reset", "reset_withdrawal_password": "Reset withdrawal password", "reset_withdrawal_password_success": "Successfully reset withdrawal password", "secondary_verification": "Secondary Verification", "security_question": "Security question", "security_verification": "Safety Verification", "select_payout_method": "Please select a payout method.", "self_exclusion": "Self-Exclusion", "self_exclusion_description": "Need a break? If you wish to begin self-exclusion, simply click the button below and follow the instructions sent to your email.", "self_exclusion_description_2": "Once self-exclusion is activated, you won't be able to withdraw, deposit, or access any games.", "set_a_withdrawal_password": "Please set a withdrawal password", "set_login_password": "Set new login password", "setup": "Set up", "set_withdrawal_password": "<PERSON> Password", "set_withdrawal_password_content": "Withdrawal password cannot be too simple, e.g., 123456", "set_withdrawal_password_success": "Successfully set withdrawal password", "six_digit_passcode": "Please enter a six-digit passcode.", "start": "Start", "start_self_exclusion_description_1": "It’s okay — we all need a break sometimes. You’ve requested to begin the self-exclusion process.", "start_self_exclusion_description_2": "To proceed, simply click the button below to activate your self-exclusion.", "start_self_exclusion_description_3": " If there is anything else we can assist you with, please feel free to contact our customer service team.", "switch_normal_verify": "Verify with Withdrawal Passcod", "switch_two_fa": "Verify with Email/Phone/Goolge", "under_review": "Under Review", "unlock_self_exclusion": "Unlock Self-Exclusion", "unlock_self_exclusion_description": "Feeling ready to come back? If you’d like to lift your self-exclusion, please click the button below and verify your request using the confirmation code.", "user_agreement1": "I have read and agree to", "user_agreement2": " Terms of Service", "user_agreement3": " User Agreement", "user_avatar_under_review": "User avatar is under review", "verification_code_placeholder": "Please enter the received verification code", "verification_submitted": "Verification Submitted", "verification_submitted_description": "Your identity verification has been submitted. If you need to update your address or ID information, please\n        contact customer support for assistance. Thank you.", "verified": "Verified", "withdrawal_management": "Withdrawal address management", "withdrawal_password_valid": "Please Enter The Withdrawal Password", "withdrawal_password_valid1": "The withdrawal password is 6 digits", "withdraw_password": "<PERSON><PERSON><PERSON> password", "withdraw_verification_content": "Used for verification when withdrawing funds or modifying security settings."}, "promo": {"activity_is_over": "Activity is over", "activity_not_started": "Activity not started", "all": "All", "claim_history": "Claim History", "event_name": "Event Name", "events": "Events", "expired": "Expiration", "inExpired": "Expired", "instruction": "Instruction", "not_started": "The activity is not started", "price": "Price", "rain_now": "Rain now", "red_envelope_broadcast": "Red Envelope Broadcast", "red_envelopes_rain": "Red Envelopes Rain", "refresh": "Refresh", "rewards": "Award", "rewards_type": "Rewards Type", "status": "Status", "task": "Task", "task_hall_popup_all": "Never remind", "task_hall_popup_claim": "Go to claim your activity rewards", "task_hall_popup_grand_prize": "Grand prize", "task_hall_popup_the_day": "The {number} day", "task_hall_popup_the_final_secret_jackpot": "The final secret jackpot", "task_hall_popup_today": "Do not remind me again today", "task_hall_popup_treasure_current_active": "Currently Active", "task_hall_popup_treasure_reset_countdown": "Treasure reset countdown", "task_hall_popup_treasure_reset_countdown_more_than_a_day": "{day} Days", "time": "Time", "to_be_collected": "Unclaimed", "total_amount_accumulated": "Total Amount Accumulated", "vip": "VIP", "wait_for_you": "Wait for you"}, "rebate": {"back": "Back", "bet": "Bet", "claim": "<PERSON><PERSON><PERSON>", "claimed": "Claimed", "expired": "Expired", "expired_after": "Expired after {time}", "insufficient_rebate_balance": "Insufficient rebate balance", "page_title": "Rebate", "receive_all": "Receive All", "total_bets": "Total Bets", "total_bet_title": "Current Total Bet", "transfer": "Transfer"}, "record": "Record", "search": {"clear_history": "Clear History", "need_search_field_length": "At least {length} characters are required to search", "recent_searches": "Recent Searches", "search": "Search"}, "sign_in_rule_explaination": "If a full cycle of consecutive check-ins is completed or interrupted, the streak will reset and start over.", "silderBar": {"accountSecurity": "Account Security", "added_frequently": "Added Frequently Used", "allianceCenter": "Alliance Center", "apiManagement": "API Management", "buyCrypTo": "BUY CRYPTO", "casino": "Casino", "favorite": "Favorite Games", "fiat_currency_order_history": "Fiat Currency Order History", "financeRecord": "Finance Record", "frequently": "Frequently Used", "frequentlyUsed": "Frequently Used", "function": "Function", "fundTransfer": "Fund Transfer", "gameProvider": "Game Provider", "gameTypeRecommend": "Game Recommendations", "gasino": "CASINO", "inviteRebate": "Refer & Earn", "liveSupport": "Live Support", "logOut": "Log Out", "max_add_frequently": "You can add up to {total} frequently used", "menu": "<PERSON><PERSON>", "myBet": "My Bets", "myRebate": "Rebate Wallet\n\n", "personalInformation": "Personal Information", "preferenceSettings": "Preference Settings", "profile": "Profile", "promotions": "Promotions", "recentGame": "Recent Games", "reportStatistics": "Statistical Report", "rewardCenter": "Offer", "setting": "Settings", "shareholderCenter": "Shareholder Center", "sports": "Sports", "subordinateReport": "Subordinate Report", "transferRecord": "Financial Records", "vipCenter": "VIP", "vipClub": "VIP Club"}, "support_program": {"action": "Action", "available": "Available", "bonus": "Bonus", "confirm_message": "Send {amount} Bonus to \"{user}\"", "date_send": "Date Send", "my_Invitation": "Ｍy Invitation", "registration_date": "Registration Date", "remaining": "Remaining", "rule_info": "Rule info", "send_bonus": "Send Bonus", "send_history": "Send History", "send_time": "Send Time", "sented": "Sented", "sent_failed": "Send Failed", "sent_successfully": "Sent Successfully", "sub_title": "At {name}, We support you", "title": "Agent Support Program", "used": "Used", "user_name": "User Name"}, "swap": {"amount": "Amount", "asset": "asset", "balace_is_no_enough": "Balance is no enough", "balance": "Balance", "betting_amount_tips": "You still need to meet a wagering requirement of {amount} {symbol} for this currency before you can swap.", "completed_convert": "Convert Completed", "confirm_convert": "Confirm Convert", "confirm_swap": "Confirm Swap", "convert": "Small Convert", "converted": "Converted", "convert_record": "Small Amount Swap", "convert_small_tips": "Assets with lower valuation can be exchanged once every {hours} hours {minutes} minutes, and delisted coins are temporarily unavailable.", "currency": "<PERSON><PERSON><PERSON><PERSON>", "exchage_record": "  Exchange Record", "exchange": "Exhange", "exchange_rate": "Exchange Rate", "fee": "Fee", "fund_account": "Funds account", "hide_zero": "Hide zero balances", "max": "Max", "min_amount": "Minimum swap amount", "ok": "ok", "refresh_price": "Refresh Price", "reset": "Reset", "settle": "Settlement time", "source_of_funds": "Source of funds", "swap": "<PERSON><PERSON><PERSON>", "swap_button_text": "Swap currencies and enjoy diverse games.", "swap_completed": "Swap Completed", "swap_record": "Swap Record", "tip1": "This quote has expired. Click to refresh the latest price.", "tips": "Exchange rate will refresh in", "transaction_fee": "Transaction Fee", "transaction_time": "Transaction time", "trans_fee_rate": "Transaction fee rate", "ur_get": "Your get", "ur_send": "Your send", "ur_swap_balance": "Available swap amount", "view_asset": "View Asset"}, "total_bonus": "Total Bonus", "try_now": "Try Now", "turnover": {"activity_name": "Activity Name", "approve_amount": "Approved Amount", "approve_time": "Apporved Time", "locked_amount": "Locked Amount", "target_turnover": "Target Turnover", "turnover_list": "Turnover List", "turnover_rule1": "You can complete the above tasks daily. Upon completion, you will receive a certain amount of bonus. The higher the difficulty, the greater the reward.", "turnover_rule2": "The bonus from this task can only be withdrawn after reaching {unlockConditions} {coinName}.", "turnover_rule3": "This task is limited to legitimate manual operations by the account holder only. Renting accounts, using plugins, bots, betting between different accounts, mutual brushing, arbitrage, API or protocol manipulation, exploiting bugs, group control, or other technical means are strictly prohibited. Violations may result in reward cancellation or deduction, account freezing, or blacklisting. To avoid misunderstandings caused by language differences, the platform reserves the final interpretation right of this activity.", "turnover_title": "Turnover Record"}, "user": {"account": "Account", "account_placeholder": "Please enter your account number", "agree_terms_service": "I have read and agree to", "associated_existing_account": "Link existing account", "associated_existing_account_tips": "This email has been registered, please verify this email, and you can log in directly through a third-party account.", "avatar_approve": "Image approval", "birthday": "Date of Birth", "birthday_placeholder": "Please select birth date", "china": "China", "confirm_password": "Confirm Password", "confirm_password_placeholder": "Please confirm password", "email": "Email", "email_bind_safety_verification": "Email binding cannot be deleted after being set as a security item, only modified or turned off", "email_code": "Email Verification Code", "email_format_error": "Email format is incorrect", "email_placeholder": "Please enter email address", "email_uid_name": "Email/UID/Login Name", "email_uid_name_placeholder": "Please enter email/UID/login name", "find_password": "Recover password", "forget_password": "Forget the password?", "i_know": "I know", "invite": "Invite Code", "invite_code": "Invite Code", "invite_placeholder": "Please enter invite code", "login": "<PERSON><PERSON>", "login_name": "Set Login Name", "login_name_already_exists": "The login name already exists, please reset it", "login_name_format_error": "Please enter 5-20 letters/letters+numbers", "login_name_modify_success": "<PERSON>rna<PERSON> set successfully", "login_name_placeholder": "Available for next login", "login_name_placeholder_2": "Please enter 5-20 characters/letters + numbers", "login_name_placeholder_3": "Please enter the length of 5-20", "login_success": "Login successful", "new_bind_phone_number": "New bound phone number", "no_search_result": "No search results", "not_receive_validate_code": "Did not receive verification code?", "optional": "Optional", "or_continue_use": "Or continue using", "password": "Password", "password_login": "Password", "password_placeholder": "Please enter password", "password_type": "Login method", "password_validate": {"contains_numbers": "Must contain numbers", "contains_whitespace": "Cannot contain spaces", "contain_uppercase_and_lowercase_letters": "Must contain uppercase and lowercase letters", "password_format_error": "Password format error", "password_length": "Password length must be 8-32 characters", "password_not_match": "Passwords do not match", "password_not_meet_the_standard": "Password does not meet the standard"}, "phone": "Phone", "phone_code": "Phone Verification Code", "phone_number": "Phone Number", "phone_number_format_error": "Phone format error", "phone_number_placeholder": "Please enter phone number", "please_send_validate_code": "Please send the verification code first", "qr_code": "QR Code", "refresh_demo": "Try Now", "register": "Register", "register_success": "Congratulations on completing registration", "resend": "Resend", "safety_verification": "Safety Verification", "select_area_code": "Search nationality/region/area code", "send": "Send", "send_receive_validate_code": "Press to send and receive verification code", "send_success": "<PERSON><PERSON> successfully", "service_agreement_must_be_selected": "Agreement must be checked", "setup_account_name": "Please set the account name", "switch_account": "Switch Account", "switch_to_email": "Switch to Email", "switch_to_phone": "Switch to Mobile", "terms_service": "Terms of Service", "third_party_account_tips": "We cannot retrieve user information from your third-party account. Please register using an email.", "try_experience": "Want to try it out?", "uid_format_error": "UID format error", "unavailable": "Unavailable", "validate_code_length": "Please enter a 6-digit verification code", "validate_code_length_6": "Verification code must be 6 digits", "verification_code": "Verification code", "verify_code_login": "Verification Code", "walletConnect": "Wallet Connect"}, "vip": {"bet": "Bet", "bonus": "Bonus", "claim_all": "Claim All", "current_level": "Current Level", "daily": "Daily Bonus", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "level": "Level", "monthly": "Monthly Bonus", "privilege": "Vip Power", "promotionBonus": "Promotion Bonus", "rule": "Rule", "to_next_level": "To next level", "total_bet": "Total bet", "total_deposit": "Total deposit", "upgradeCondition": "Upgrade Condition", "upgrade_condition1": "To be promoted to the next level, additional deposits and wagers must be made on top of the existing accumulated amounts.", "upgrade_condition2": "For example:", "upgrade_condition3": "To reach VIP1, a deposit of $1,000 and wagers of $2,000 are required.", "upgrade_condition4": "To reach VIP2, another deposit of $1,000 and wagers of $2,000 are required.", "upgrade_condition5": "This means a member must accumulate a total deposit and wager amount of $3,000 ($1,000 + $2,000) to be promoted to VIP2, and so on.", "vip_rule": "VIP Rule", "vip_rule_content4": "To reach VIP2, another deposit of $1,000 and wagers of $2,000 are required.", "vip_rule_content5": "This means a member must accumulate a total deposit and wager amount of $3,000 ($1,000 + $2,000) to be promoted to VIP2, and so on.", "weekly": "Weekly Bonus"}, "vip_promotion": "VIP promotion bonus", "vip_reward": "VIP rewards", "wallet_auth": {"solana_tip": "Please refresh the page after installing Solana. Have you finished the installation? Refresh now?", "solana_tip1": "Please log in or unlock your Solana wallet first", "tron_tip": "Please refresh the page after installing TronLink. Have you finished the installation? Refresh now?", "tron_tip1": "Please log in or unlock your TronLink wallet first"}, "wallet": {"auto_deposit_describe": "Third-party payment automatic crediting, fast arrival", "auto_withdraw_describe": "Supports multiple common payment methods to quickly sell the assets", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "deposit_crypto": "Recharge Cryptocurrency", "deposit_crypto_docs": "Get a recharge address for BTC, ETH or other cryptocurrencies and recharge via the blockchain.", "deposit_page_title": "Deposit Crypto", "display_cryptocurrency_in_fiat_currency": "Display cryptocurrency in fiat currency", "fiat_recharge": "Fiat Recharge", "fiat_withdraw": "Fiat Withdrawal", "form_based_deposit": "Form-based deposit", "form_deposit_describe": "Large deposit manual crediting requires T+2 processing time", "have_digital_assets": "Already own digital assets", "no_crypto_assets": "I have no cryptocurrency assets", "note_these_are_approximate_currency_values": "Note: These are approximate currency values.", "set_wallet_currency": "Set Wallet Currency", "third_party": "Third-party Payment", "withdraw": "Withdraw", "withdraw_crypto": "Withdraw Cryptocurrency", "withdraw_crypto_docs": "Withdraw BTC, ETH, or other cryptocurrencies via blockchain"}, "withdraw": {"address": "Select Payment Account", "amount": "<PERSON><PERSON><PERSON> Amount", "amount_exceed_balance": "Exceeding available withdrawal balance", "amount_exceed_max": "Must not exceed the maximum withdrawal amount", "amount_exceed_min": "Must not be less than the minimum withdrawal amount", "available_balance": "Available Balance is {balance}{areaCurrency}", "bank_name": "Bank Name", "enter_amount": "Please enter the withdrawal amount", "method": "Select Payment Method", "need_betting_amount": "Need Rebate: ", "select_address": "Select Account", "select_method": "Select Method", "title": "Fiat Withdraw", "withdraw_failed": "Withdrawal Failed", "withdraw_success": "<PERSON><PERSON><PERSON> Successful"}, "withdrawal_address": {"action": "Action", "address": "Address", "add_withdrawal_address": "<PERSON><PERSON> Address", "add_withdrawal_address_success": "Successfully added the withdrawal address", "alert": "When you sell cryptocurrency, your receiving account will receive fiat currency.", "cancel": "Cancel", "confirm": "Confirm", "delete": "Delete", "delete_payout_account": "Delete Withdrawal Address", "delete_payout_account_success": "Successfully deleted the withdrawal address", "delete_payout_account_tips": "Are you sure you want to delete the withdrawal address {bank}?", "edit": "Edit", "edit_withdrawal_address": "<PERSON> Address", "edit_withdrawal_address_success": "Successfully edited the withdrawal address", "empty_withdrawal_address": "Empty withdrawal address", "enter_address": "Please enter the withdrawal address", "enter_remark": "Please enter the remark", "enter_select_currency": "Please select the currency", "remark": "Remark", "select_currency": "Select Currency", "title": "<PERSON><PERSON><PERSON> Address", "withdrawal_address": "<PERSON><PERSON><PERSON> Address"}, "withdraw_detail": {"add_address": "Add Frequently Used Address", "add_payout": "Add payout account", "address": "Address", "address_required": "Address Is Required", "address_tips": "Plase Enter <PERSON>drawal Address", "add_withdrawal_address": "<PERSON><PERSON> Address", "all_currencies": "All currencies", "all_withdraw_records": "All Withdraw Records", "amount_required": "<PERSON><PERSON><PERSON> Amount Must Be Filled In", "available": "Available", "bank_accoun": "Bank Account", "can_mention": "Can mention:", "copy_chain_address": "Click To Copy The Chain Address", "crypto_required": "Crypto Is Required", "fast_deposit": "Fast Deposit", "internal_transfer": "Internal transfer (free)", "internal_transfer_v2": "Internal Transfer", "low_cost": "Low Cost", "max_available": "Max Available Amount", "max_withdrawal_amount": "<PERSON> Amount", "minumun": "<PERSON><PERSON><PERSON>", "min_withdraw_count": "Minimum Withdrawal Amount", "network": "Network", "network_fee": "Network Fee", "no_currencies_found": "No currencies found", "onchain_withdrawal": "On-Chain Withdraw", "plase_network": "Plase Select A Transfer Network", "plase_uid": "Plase Enter The Repipient's UID", "please_number": "Please enter a valid positive number", "receive_amount": "Receive Amount", "recipient_uid": "Recipient's UID", "remark_tips": "Please enter remarks(optional,99characters)", "secure_stable": "Secure & Stable", "select_crypto": "Select Crypto", "select_network": "Select Network", "transfer": "Transfer", "uid": "UID", "uid_required": "Recipient's UID Must Be Filled In", "withdraw": "Withdraw", "withdrawable_currencies": "Withdrawable Currencies", "withdrawal": "<PERSON><PERSON><PERSON>", "withdraw_amount": "Withdraw Amount", "withdraw_browser_sequrity": "Ensure your computer and browser are secure to prevent data tampering or leaks", "withdraw_details": "<PERSON><PERSON><PERSON>", "withdraw_fail": "Withdraw Fail", "withdraw_records": "Withdraw Records", "withdraw_security_tips": "To ensure fund security, manual review is required for withdrawals when your account security settings\n            change or your password is modified. Please wait for staff to contact you via phone or email", "withdraw_success": "Withdraw Success"}}