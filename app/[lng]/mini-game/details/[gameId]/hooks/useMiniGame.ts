import { useState, useMemo, useEffect, useCallback, useRef } from 'react'
import { useGetMiniGameDetails } from './useGetMiniGameDetails'
import { useMiniGameTryLink } from './useMiniGameTryLink'
import { MiniGameUnLoginProps } from '../client-unlogin-page'
import { MiniGameLoginedProps } from '../client-logined-page'
import { useMiniGameRealLink } from './useMiniGameRealLink'
import { Coin, createApi, GameDetailResponse, GameLink, WalletBalance } from '@libs/apis/src'
import { MiniGameButton } from '../components/mini-game-buttons'
import { AppIconEnum, BUTTON_TYPE, MINI_GAME_SETTING, MINI_GAME_SETTING_ICON } from '../contants'
import { MiniGameSetting } from '../components/mini-game-settings'
import { useRouter } from 'next/navigation'
import { KeyedMutator } from 'swr'
import { useStaticTools } from '@/store/static-tools'
import { SharedApp } from '../components/mini-game-shared'
import { useTranslate } from '@tolgee/react'
import { useUserWalletStore } from '@/store/user-wallet'
import { useUserStore } from '@/store/user'
import { TabsTypeLogRegisterType } from '@/enums/login'
import { UserAccountTypeEnum } from '@/enums/user'
import { useAuth } from '@modules/auth/src'
import { useLayoutStore } from '@/store/layout'
import { useDepositWithdrawStore } from '@/components/client/deposit-withdraw/store'
import { openThirdPartyUrl } from '@/utils/open-page'
import { useTrackEvent, EventTrackType } from '@/utils/analytics'
export const useMiniTryLink = (params: MiniGameUnLoginProps) => {
  const { gameId, groupId, lng } = params
  const {
    gameDetails,
    isLoading: gameDetailsLoading,
    gameDetailsReload,
  } = useGetMiniGameDetails({
    groupId,
    gameId: gameId || '',
  })
  const coin = gameDetails?.coins?.[0] || {}
  const coinName = coin?.coinName || ''
  const {
    miniGameTryLink,
    isLoading: tryLinkLoading,
    reloadTryLink,
  } = useMiniGameTryLink(
    coinName
      ? {
          groupId,
          gameId,
          language: lng,
          coinName: gameDetails?.coins?.[0]?.coinName || '',
        }
      : null,
  )

  return {
    gameDetails,
    gameDetailsLoading,
    tryLinkLoading,
    miniGameTryLink,
    coin,
    gameDetailsReload,
    reloadTryLink,
  }
}

export const useMiniRealLink = (params: MiniGameLoginedProps) => {
  const { gameId, groupId, lng } = params
  const {
    gameDetails,
    isLoading: gameDetailsLoading,
    gameDetailsReload,
  } = useGetMiniGameDetails({
    groupId,
    gameId: gameId || '',
  })

  const { walletInfo } = useUserWalletStore()
  const coin = {
    coinId: walletInfo?.coinId || '',
    coinName: walletInfo?.coinName || '',
    webLogo: walletInfo?.webLogo,
    appLogo: walletInfo?.appLogo,
  }
  const {
    miniGameRealLink,
    isLoading: realLinkLoading,
    reloadRealLink,
  } = useMiniGameRealLink(
    coin?.coinName
      ? {
          groupId,
          gameId,
          language: lng,
          coinName: coin.coinName,
        }
      : null,
  )

  return {
    gameDetails,
    gameDetailsLoading,
    realLinkLoading,
    miniGameRealLink,
    coin,
    walletInfo,
    gameDetailsReload,
    reloadRealLink,
  }
}

export const useMiniGameButtons = (gameDetails?: GameDetailResponse | null) => {
  const { t } = useTranslate()
  const { isLogin, data } = useAuth()
  const userInfo = data?.userInfo
  const miniGameButtons = useMemo(() => {
    const realButton: MiniGameButton = {
      text: t('mini_game.real_mode'),
      buttonClass: 'bg-primary-1',
      icon: 'rightArrowSolid',
      iconClass: 'w-4 h-4 text-primary-1',
      showSpin: true,
      type: BUTTON_TYPE.REAL,
      styleType: 'primary',
    }

    const trialButton: MiniGameButton = {
      text: t('mini_game.try_mode'),
      buttonClass: 'bg-gs-500',
      icon: 'rightArrowSolid',
      iconClass: 'w-4 h-4 text-gs-500',
      showSpin: true,
      type: BUTTON_TYPE.TRY,
      styleType: 'default',
    }

    const loginButton: MiniGameButton = {
      text: t('mini_game.reg_login'),
      buttonClass: 'bg-primary-1',
      icon: null,
      iconClass: null,
      showSpin: false,
      type: BUTTON_TYPE.LOGIN,
      styleType: 'primary',
    }
    const supportAnonymous = gameDetails?.isSupportAnonymous
    if (isLogin && userInfo?.memberTag === UserAccountTypeEnum.demoUser) {
      if (supportAnonymous === 2) {
        return []
      } else {
        return [trialButton]
      }
    }

    if (supportAnonymous === 2) {
      return isLogin ? [realButton] : [loginButton]
    }

    return isLogin ? [realButton, trialButton] : [loginButton, trialButton]
  }, [isLogin, gameDetails?.isSupportAnonymous, userInfo])

  return {
    miniGameButtons,
  }
}

export const useMiniGameSettings = (isTheater: boolean, gameDetails?: GameDetailResponse | null) => {
  const { t } = useTranslate()
  const [showHighlight, setShowHighlight] = useState(-1)
  const miniGameSetting = useMemo(() => {
    // const shared: MiniGameSetting = {
    //   title: t('mini_game.share'),
    //   icon: MINI_GAME_SETTING_ICON.MINI_GAME_SHARED,
    //   highlightIcon: MINI_GAME_SETTING_ICON.MINI_GAME_HIGHLIGHT_SHARED,
    //   type: MINI_GAME_SETTING.SHARED,
    //   iconClass: 'w-6 h-5',
    // }

    const fullScreen: MiniGameSetting = {
      title: t('mini_game.full_screen'),
      icon: MINI_GAME_SETTING_ICON.MINI_GAME_FULL_SCREEN,
      type: MINI_GAME_SETTING.FULL_SCREEN,
      highlightIcon: MINI_GAME_SETTING_ICON.MINI_GAME_HIGHLIGHT_FULL_SCREEN,
      iconClass: 'w-6 h-6',
    }

    const back: MiniGameSetting = {
      title: t('mini_game.back'),
      icon: MINI_GAME_SETTING_ICON.MINI_GAME_BACK,
      type: MINI_GAME_SETTING.BACK,
      highlightIcon: MINI_GAME_SETTING_ICON.MINI_GAME_HIGHLIGHT_BACK,
      iconClass: 'w-6 h-6',
    }
    const star: MiniGameSetting = {
      title: gameDetails?.isCollection ? t('mini_game.cancel_collection', {}) : t('mini_game.collection', {}),
      icon: gameDetails?.isCollection
        ? MINI_GAME_SETTING_ICON.MINI_GAME_HIGHLIGHT_STAR
        : MINI_GAME_SETTING_ICON.MINI_GAME_STAR,
      type: MINI_GAME_SETTING.STAR,
      highlightIcon: MINI_GAME_SETTING_ICON.MINI_GAME_HIGHLIGHT_STAR,
      iconClass: 'w-6 h-6',
    }

    const theater: MiniGameSetting = {
      title: t('mini_game.theater'),
      icon: isTheater ? MINI_GAME_SETTING_ICON.MINI_GAME_HIGHLIGHT_THEATER : MINI_GAME_SETTING_ICON.MINI_GAME_THEATER,
      highlightIcon: MINI_GAME_SETTING_ICON.MINI_GAME_HIGHLIGHT_THEATER,
      type: MINI_GAME_SETTING.THEATER,
      iconClass: 'w-6 h-6',
    }

    return [star, theater, fullScreen, back]
  }, [gameDetails?.isCollection, isTheater])

  return {
    showHighlight,
    setShowHighlight,
    miniGameSetting,
  }
}

type UseMiniGameDispatchParams = {
  lng: string
  coin: Coin
  miniGameUrl: GameLink
  gameDetails?: GameDetailResponse | null
  walletInfo?: WalletBalance | null
  gameDetailsReload: KeyedMutator<GameDetailResponse | null>
  handleModal: (isOpen: boolean) => void
  reloadGameLink: KeyedMutator<GameLink | null>
}

export const useMiniGameDispatch = ({
  lng,
  coin,
  miniGameUrl,
  gameDetails,
  walletInfo,
  gameDetailsReload,
  handleModal,
}: UseMiniGameDispatchParams) => {
  const router = useRouter()
  const { isLogin, data } = useAuth()
  const { t } = useTranslate()
  const { layoutData, setIsPlaying } = useLayoutStore()
  const { message } = useStaticTools()
  const [theater, setTheater] = useState<boolean>(false)
  const [fullScreen, setFullScreen] = useState<boolean>(false)
  const [gameUrl, setGameUrl] = useState<GameLink | null>(miniGameUrl)
  const [showMiniGameScreen, setShowMiniGameScreen] = useState<boolean>(true)
  const [showTryMode, setShowTryMode] = useState<string>('')
  const [showWarningPopup, setShowWarningPopup] = useState<boolean>(false)
  const { setShowDepositWithdrawDrawer, openSetCurrentMenu } = useDepositWithdrawStore()
  const { setLogRegisterVisible, setRegisterLoginModalActiveKey } = useUserStore()
  const currenCoin = useRef('')
  const firstRender = useRef(true)
  const clickCollection = useRef(false)
  const isshowV2Deposit = useMemo(
    () => ['asia-single', 'betfury'].includes(layoutData?.dbJson?.config?.walletMode || ''),
    [layoutData?.dbJson],
  )
  const trackEvent = useTrackEvent()
  useEffect(() => {
    setGameUrl(miniGameUrl || null)
  }, [miniGameUrl])

  const resetMiniGame = useCallback(() => {
    setGameUrl(null)
    setShowMiniGameScreen(true)
    setShowTryMode('')
  }, [])

  useEffect(() => {
    if (firstRender.current && data?.userInfo) {
      firstRender.current = false
      return
    }

    if (!firstRender.current && isLogin && walletInfo?.coinName) {
      resetMiniGame()
    }
  }, [walletInfo?.coinName])

  useEffect(() => {
    return () => {
      firstRender.current = true
    }
  }, [])

  useEffect(() => {
    setIsPlaying(true)
    return () => {
      setIsPlaying(false)
    }
  }, [])

  const openSwap = useCallback(() => {
    if (
      gameDetails?.coins?.length &&
      walletInfo?.coinId &&
      currenCoin.current !== walletInfo?.coinId &&
      data?.userInfo?.memberTag !== UserAccountTypeEnum.demoUser
    ) {
      currenCoin.current = walletInfo.coinId
      const isSupprot = gameDetails?.coins?.some((coin) => coin?.coinId === walletInfo?.coinId)
      if (isSupprot) {
        setShowDepositWithdrawDrawer(false)
      } else {
        setShowDepositWithdrawDrawer(true)
        if (isshowV2Deposit) {
          openSetCurrentMenu('swap')
        }
        resetMiniGame()
      }
    }
  }, [gameDetails?.coins, walletInfo?.coinId, data?.userInfo])

  useEffect(() => {
    if (clickCollection.current) {
      return
    }
    openSwap()
  }, [gameDetails, walletInfo?.coinId, data?.userInfo])

  const handleGameRealLink = async () => {
    try {
      const { data } = await createApi().apis.game.getGameLink({
        data: {
          groupId: gameDetails?.groupId || '',
          gameId: gameDetails?.id || '',
          coinName: walletInfo?.coinName,
          language: lng,
        },
      })
      trackEvent(EventTrackType.StartGame, {
        game_name: gameDetails?.name,
        currency: walletInfo?.coinName,
      })
      setGameUrl(data)
    } catch (err) {
      message.error(t('mini_game.game_error'))
      throw err
    }
  }

  const handleGameTryLink = async () => {
    try {
      const { data } = await createApi().apis.game.getAnonymousGameLink({
        data: {
          groupId: gameDetails?.groupId || '',
          gameId: gameDetails?.id || '',
          coinName: coin?.coinName,
          language: lng,
        },
      })
      setGameUrl(data)
    } catch (err) {
      message.error(t('mini_game.game_error'))
      throw err
    }
  }

  const handleButtonClick = (button: MiniGameButton) => {
    switch (button.type) {
      case BUTTON_TYPE.REAL:
        setShowMiniGameScreen(false)
        if (walletInfo?.coinId !== coin?.coinId) {
          setGameUrl(null)
          handleGameRealLink()
        }
        return
      case BUTTON_TYPE.TRY:
        setShowMiniGameScreen(false)
        if (isLogin && data?.userInfo?.memberTag !== UserAccountTypeEnum.demoUser) {
          handleGameTryLink()
          setShowTryMode(t('mini_game.try_mode'))
        }
        return
      case BUTTON_TYPE.LOGIN:
        setLogRegisterVisible(true)
        setRegisterLoginModalActiveKey(TabsTypeLogRegisterType.login)
        return
      default:
        return
    }
  }

  const collectGame = async () => {
    try {
      clickCollection.current = true
      const isCancel = gameDetails?.isCollection ? true : false
      const { data } = await createApi().apis.game.postCollectGame({
        data: {
          ids: `${gameDetails?.id || ''}`,
          isCancel,
        },
      })
      if (data.success) {
        trackEvent(EventTrackType.CollectGame, {
          game_name: gameDetails?.name,
        })
        message.success(!isCancel ? t('mini_game.coll_game') : t('mini_game.cancel_coll_game'))
        gameDetailsReload()
      } else {
        message.error(!isCancel ? t('mini_game.coll_game_failed') : t('mini_game.cancel_coll_game_failed'))
      }
    } catch (err) {
      console.log(err)
      message.error(t('mini_game.coll_game_failed'))
    } finally {
      clickCollection.current = false
    }
  }

  const handleSettingClick = (miniGameSetting: MiniGameSetting) => {
    switch (miniGameSetting.type) {
      case MINI_GAME_SETTING.SHARED:
        handleModal(true)
        return
      case MINI_GAME_SETTING.STAR:
        if (isLogin) {
          collectGame()
        } else {
          message.error(t('mini_game.no_login'))
        }
        return
      case MINI_GAME_SETTING.THEATER:
        setTheater(!theater)
        return
      case MINI_GAME_SETTING.FULL_SCREEN:
        if (!showMiniGameScreen) {
          setTheater(false)
          setFullScreen(true)
        }
        return
      case MINI_GAME_SETTING.BACK:
        router.back()
        return
      default:
        return
    }
  }

  return {
    gameUrl,
    theater,
    showMiniGameScreen,
    fullScreen,
    showTryMode,
    showWarningPopup,
    handleButtonClick,
    handleSettingClick,
    setFullScreen,
    setShowWarningPopup,
  }
}

export const useMiniSharedMedia = (gameId: string | undefined, groupId: string, lng: string) => {
  const [openModal, setOpenModal] = useState(false)
  const shareUrl = `${window.location.origin}/${lng}/mini-game/details/${gameId}?groupId=${groupId}`

  const facebook: SharedApp = {
    icon: AppIconEnum.FACEBOOK,
    title: 'Facebook',
    iconClass: 'w-10 h-10',
    shareUrl: `https://www.facebook.com/sharer/sharer.php?u=${shareUrl}`,
  }

  const telegram: SharedApp = {
    icon: AppIconEnum.TELEGRAM,
    title: 'Telegram',
    iconClass: 'w-10 h-10',
    shareUrl: `https://t.me/share/url?url=${shareUrl}`,
  }

  const twitter: SharedApp = {
    icon: AppIconEnum.TWITTER,
    title: 'Twitter',
    iconClass: 'w-10 h-10',
    shareUrl: `https://twitter.com/intent/tweet?url=${shareUrl}`,
  }

  const email: SharedApp = {
    icon: AppIconEnum.EMAIL,
    title: 'Email',
    iconClass: 'w-10 h-10',
    shareUrl: `mailto:?subject=Check out this invite link&body=Hi,Check out this invite link: ${shareUrl}`,
  }

  const handleModal = (isOpen: boolean) => setOpenModal(isOpen)
  const openApp = (socialApp: SharedApp) => {
    if (socialApp.icon === AppIconEnum.EMAIL) {
      window.location.href = socialApp.shareUrl || ''
    } else {
      openThirdPartyUrl({
        type: 'link',
        data: socialApp.shareUrl || '',
      })
    }
  }
  return {
    openModal,
    copyUrl: shareUrl,
    sharedList: [facebook, telegram, twitter, email],
    handleModal,
    openApp,
  }
}
