'use client'

import { cn } from '@libs/utils/src'
import React, { useEffect, useRef, useState } from 'react'

// utils
import { padNumberString } from '@/components/client/lucky-wheel/utils'

// types
import type { LuckyWheelProps } from '.'

// images
import spin from '@/components/client/lucky-wheel/images/neon-b/spin.png'
import turntable from '@/components/client/lucky-wheel/images/neon-b/turntable.png'
import { LottieS3 } from '../../../lottie-s3'

const LuckyWheel = React.forwardRef<
  {
    handleSpin: (stopIndex: number) => void
  },
  LuckyWheelProps & {
    onStart?: (index: number) => void
    handleStart: () => void
  }
>(({ width = 466, className, prizeList, onStart, onEnd, handleStart }, ref) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [isSpinning, setIsSpinning] = useState(false)
  // 存储已加载的图片
  const [loadedImages, setLoadedImages] = useState<{ [key: string]: HTMLImageElement }>({})

  // 预加载所有图片
  useEffect(() => {
    const preloadImages = async () => {
      const images: { [key: string]: HTMLImageElement } = {}
      const loadPromises = prizeList.map((item, index) => {
        return new Promise<void>((resolve) => {
          const img = new Image()
          img.src = item.icon?.src || ''
          img.onload = () => {
            images[index] = img
            resolve()
          }
          img.onerror = () => resolve() // 即使加载失败也继续
        })
      })

      await Promise.all(loadPromises)
      setLoadedImages(images)
    }

    preloadImages()
  }, [prizeList])

  // 处理开始抽奖
  const handleSpin = (stopIndex: number) => {
    if (isSpinning) return
    setIsSpinning(true)
    onStart?.(stopIndex)

    // 模拟抽奖动画
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    let currentRotation = 0
    let speed = 0
    const acceleration = 0.005 // 加速度
    const maxSpeed = 0.4 // 最大速度
    const deceleration = 0.1 // 减速度
    const totalSpins = 6 // 总旋转圈数
    const targetPrize = stopIndex ?? Math.floor(Math.random() * prizeList.length) // 根据 stopIndex 或随机选择奖品
    // 计算目标角度：总圈数 * 2π + (奖品索引 * 每份奖品的角度) + (每份奖品角度的一半，使其居中)
    const targetAngle =
      Math.PI * 2 * totalSpins + (targetPrize * (Math.PI * 2)) / prizeList.length + Math.PI / prizeList.length
    let isDecelerating = false

    const animate = () => {
      if (currentRotation >= targetAngle) {
        setTimeout(() => {
          setIsSpinning(false)
          console.log('恭喜你抽中了' + prizeList[targetPrize].label)
          onEnd?.(prizeList[targetPrize].label)
        }, 3 * 1000)
        return
      }

      // 动画分三个阶段：加速、匀速、减速
      if (!isDecelerating && speed < maxSpeed) {
        speed += acceleration // 加速阶段
      } else if (currentRotation >= targetAngle * 0.8) {
        isDecelerating = true
        speed = Math.max(0.01, deceleration) // 减速阶段
      }

      currentRotation += speed

      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // 绘制旋转的转盘部分
      ctx.save()
      ctx.translate(canvas.width / 2, canvas.height / 2)
      ctx.rotate(currentRotation)
      ctx.translate(-canvas.width / 2, -canvas.height / 2)
      drawWheelContent(ctx)
      ctx.restore()

      requestAnimationFrame(animate)
    }

    animate()
  }

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // 设置画布大小
    canvas.width = width
    canvas.height = width

    // 等待图片加载完成后再绘制
    if (Object.keys(loadedImages).length === prizeList.length) {
      drawWheelContent(ctx)
    }
  }, [loadedImages])

  // 绘制转盘内容（不包括中心按钮）
  const drawWheelContent = (ctx: CanvasRenderingContext2D) => {
    const centerX = canvasRef.current!.width / 2
    const centerY = canvasRef.current!.height / 2

    const radius = canvasRef.current!.width / 2

    const prizeCount = prizeList.length
    const borderWidth = 4 // 边框宽度
    const gap = 0 // 间隔宽度

    // 计算每个扇形的角度（包括间隔）
    const totalAnglePerPrize = (Math.PI * 2) / prizeCount
    const actualAnglePerPrize = totalAnglePerPrize - gap / radius // 实际每个扇形的角度（减去间隔）

    // 从12点钟方向开始绘制，并且逆时针绘制
    let startAngle = -Math.PI / 2 // 从12点钟方向开始

    // 先绘制所有扇形的填充和边框
    prizeList.forEach((prize, index) => {
      const endAngle = startAngle - actualAnglePerPrize

      // 绘制扇形填充
      ctx.beginPath()
      ctx.moveTo(centerX, centerY)
      ctx.arc(centerX, centerY, radius - borderWidth - 8, startAngle, endAngle, true)
      ctx.lineTo(centerX, centerY)
      ctx.closePath()

      // 创建渐变填充
      const gradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius)
      if (prize.isHighlight) {
        gradient.addColorStop(0, '#FFD771')
      } else {
        if (index % 2 === 0) {
          gradient.addColorStop(0, '#5EA9F7')
          gradient.addColorStop(1, '#CE3D50')
        } else {
          gradient.addColorStop(0, '#CE3D50')
          gradient.addColorStop(1, '#5EA9F7')
        }
      }
      ctx.fillStyle = gradient
      ctx.fill()

      // 绘制边框
      ctx.lineWidth = borderWidth
      ctx.stroke()

      // 绘制文字和图标
      ctx.save()
      ctx.translate(centerX, centerY)
      ctx.rotate((startAngle + endAngle) / 2)

      // 使用预加载的图片绘制图标
      if (loadedImages[index]) {
        ctx.drawImage(loadedImages[index], radius - width * 0.15, -width * 0.075, width * 0.15, width * 0.15)
      }
      // 绘制文字
      ctx.textAlign = 'center'
      ctx.textBaseline = 'middle' // 设置文字基线为顶部，方便向下偏移
      ctx.translate(-25, 0) // 文字向下偏移
      ctx.fillStyle = '#ffffff'
      ctx.font = `bold ${width * 0.06}px -apple-system` // 设置粗体和苹果字体
      ctx.fillText(padNumberString(prize.label), radius * 0.7, 0)
      ctx.restore()
      startAngle = endAngle
    })
  }

  // 将 handleSpin 暴露给父组件
  React.useImperativeHandle(ref, () => ({
    handleSpin,
  }))

  return (
    <div
      className={cn('relative pt-[20px]', className)}
      style={{
        width: `${width}px`,
      }}
    >
      <div
        className={cn('absolute', {
          'animate-[spin_36s_linear_infinite]': !isSpinning,
        })}
        style={{
          width: `${width * 0.82}px`,
          height: `${width * 0.82}px`,
          left: '9.3%',
          top: '16%',
        }}
      >
        <LottieS3
          lottieSource={'https://tw-s3-image.s3.ap-southeast-1.amazonaws.com/lottie/luckwheel-2-spin.json'}
          className={cn('m-auto scale-[2.1]')}
        />
      </div>

      <div
        className="relative z-[1]"
        style={{
          width: `${width}px`,
          height: `${width}px`,
          backgroundImage: `url('${turntable.src}')`,
          backgroundPosition: 'center',
          backgroundSize: 'cover',
        }}
      >
        <canvas
          ref={canvasRef}
          className={cn('absolute', {
            'animate-[spin_36s_linear_infinite]': !isSpinning,
          })}
          style={{
            width: `${width * 0.82}px`,
            height: `${width * 0.82}px`,
            left: '9.3%',
            top: '9.3%',
          }}
          onClick={handleStart}
        />

        <div
          className="pointer-events-none absolute top-[0px]"
          style={{
            width: `${width * 0.12}px`,
            height: `${width * 0.17}px`,
            left: `${width * 0.44}px`,
            top: `${width * 0.24}px`,
          }}
        >
          <LottieS3
            lottieSource={'https://tw-s3-image.s3.ap-southeast-1.amazonaws.com/lottie/luckwheel-2-win.json'}
            className={cn('m-auto scale-[4]')}
          />
        </div>

        <div
          className="pointer-events-none absolute"
          style={{
            width: `${width * 0.22}px`,
            height: `${width * 0.22}px`,
            left: `${width * 0.4}px`,
            top: `${width * 0.4}px`,
            backgroundImage: `url('${spin.src}')`,
            backgroundPosition: 'center',
            backgroundSize: 'cover',
          }}
        />
      </div>
    </div>
  )
})

LuckyWheel.displayName = 'LuckyWheel'

export default LuckyWheel
