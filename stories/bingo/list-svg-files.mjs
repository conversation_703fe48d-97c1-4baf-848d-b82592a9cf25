import fs from 'fs';
import path from 'path';

const __dirname = new URL('.', import.meta.url).pathname;
const svgPath = 'public/bingo/svg'
const targetPath = path.join(__dirname, '../../', svgPath)
const outputPath = path.join(__dirname, 'svg-files.json')

/**
 * 腳本功能：
 * 1. 遞迴讀取 public/svg/bingo-web 資料夾中的所有 SVG 檔案路徑
 * 2. 將 SVG 檔案路徑輸出至 svg-files.json
 */

const files = listFilesRecursive(targetPath)
const svgFiles = files.filter(file => file.endsWith('.svg'))
const jsonContent = JSON.stringify(svgFiles, null, 2)
fs.writeFileSync(outputPath, jsonContent, 'utf8')
console.log('已將 SVG 檔案清單輸出至 svg-files.json')

export default function listFilesRecursive(dir, fileList = []) {
    const files = fs.readdirSync(dir);

    files.forEach(file => {
        const filePath = path.join(dir, file);
        const stats = fs.statSync(filePath);

        if (stats.isDirectory()) {
            listFilesRecursive(filePath, fileList);
        } else {
            // "/Users/<USER>/new-digit/zz-web/public/bingo/svg/viewport/save-on.svg".split('public/bingo/svg')
            const subPath = filePath.split(svgPath)[1]

            // [ '/Users/<USER>/new-digit/zz-web/', '/viewport/save-on.svg' ]
            const displayPath = path.join(svgPath, subPath)

            // "public/bingo/svg/arrows/arrow-down.svg"
            fileList.push(displayPath)
            // [..., "public/bingo/svg/arrows/arrow-down.svg"]
        }
    });

    return fileList;
}
