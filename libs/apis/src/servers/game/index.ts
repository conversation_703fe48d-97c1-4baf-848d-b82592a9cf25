import type { ApiItem } from '../../interface'
import type { GamePath } from './interface'

export const game: Record<GamePath, ApiItem> = {
  getSidebarGameList: {
    default: {
      url: '/xc-game/v1/ra-hall/sidebar/game/list',
      method: 'GET',
    },
  },
  getGameRecords: {
    default: {
      url: '/xc-game/v1/ra-hall/gameRecord',
      method: 'GET',
    },
  },
  getUnsettledGameRecords: {
    default: {
      url: '/xc-game/v1/ra-hall/gameRecord/unsettled',
      method: 'GET',
    },
  },
  getGroupList: {
    default: {
      url: '/xc-game/v1/ra-hall/group/list',
      method: 'GET',
    },
  },
  getGameList: {
    default: {
      url: '/xc-game/v1/ra-hall/game/list',
      method: 'GET',
    },
  },
  getTabGameList: {
    default: {
      url: '/xc-game/v1/ra-hall/tab/game/list',
      method: 'GET',
    },
  },
  getRecentGames: {
    default: {
      url: '/xc-game/v1/ra-hall/game/recent',
      method: 'GET',
    },
  },
  getCollectGames: {
    default: {
      url: '/xc-game/v1/ra-hall/game/collect',
      method: 'GET',
    },
  },
  postCollectGame: {
    default: {
      url: '/xc-game/v1/ra-hall/game/collect',
      method: 'POST',
    },
  },
  getGameDetail: {
    default: {
      url: '/xc-game/v1/ra-hall/game/detail',
      method: 'GET',
    },
  },
  getRecommendGames: {
    default: {
      url: '/xc-game/v1/ra-hall/game/recommend',
      method: 'GET',
    },
  },
  getHotGames: {
    default: {
      url: '/xc-game/v1/ra-hall/game/hot',
      method: 'GET',
    },
  },
  getGameGroup: {
    default: {
      url: '/xc-game/v1/ra-hall/game-group-supplier',
      method: 'GET',
    },
  },
  getGameLink: {
    default: {
      url: '/xc-game/v1/ra-hall/game/startV2',
      method: 'GET',
    },
  },
  getAnonymousGameLink: {
    default: {
      url: '/xc-game/v1/ra-hall/game/startAnonymous',
      method: 'GET',
    },
  },
}
