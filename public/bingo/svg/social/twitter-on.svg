<svg viewBox="0 0 23 23" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g filter="url(#filter0_i_6823_51870)">
    <g clip-path="url(#clip0_6823_51870)">
      <path
        d="M17.6064 2.08337H20.6986L13.9091 9.81393L21.8414 20.3006H15.6166L10.743 13.9279L5.16352 20.3006H2.0713L9.26408 12.0323L1.66797 2.08337H8.04736L12.4504 7.90482L17.6064 2.08337ZM16.5241 18.4856H18.2382L7.14658 3.83115H5.30469L16.5241 18.4856Z"
        fill="#FFC039" />
    </g>
  </g>
  <defs>
    <filter id="filter0_i_6823_51870" x="0.825195" y="0.25" width="22" height="22" filterUnits="userSpaceOnUse"
      color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="1.5" />
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
      <feColorMatrix type="matrix" values="0 0 0 0 0.984314 0 0 0 0 0.419608 0 0 0 0 0.0705882 0 0 0 0.71 0" />
      <feBlend mode="normal" in2="shape" result="effect1_innerShadow_6823_51870" />
    </filter>
    <clipPath id="clip0_6823_51870">
      <rect width="20.1667" height="18.2172" fill="white" transform="translate(1.74219 2.08337)" />
    </clipPath>
  </defs>
</svg>
