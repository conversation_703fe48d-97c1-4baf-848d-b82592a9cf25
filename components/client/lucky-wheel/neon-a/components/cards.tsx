import { cn } from '@libs/utils/src'
import { motion } from 'motion/react'
import Image from 'next/image'
import { useRef, useState } from 'react'

// images
import BG_REWARD from '@/components/client/lucky-wheel/images/neon-a/bg-reward.png'
import CloseIcon from '@/components/client/lucky-wheel/images/neon-a/button-close.png'
import MusicOffIcon from '@/components/client/lucky-wheel/images/neon-a/button-music-off.png'
import MusicIcon from '@/components/client/lucky-wheel/images/neon-a/button-music.png'

// audios
import { backgroundMusic1, closeCancelAudio } from '@/components/client/lucky-wheel/audios'

// utils
import { formatNumber } from '@/libs/apis/src/utils'

// components
import { BtnShare, BtnStart } from './buttons'
import { TextLoseImage, TextWinImage } from './texts'
import { TitleLoseImage, TitleWinImage } from './title'

// types
import { BackdropProps, BaseCardProps, CardProps } from '@/components/client/lucky-wheel/types'

// constants
const DEFAULT_WIDTH = 345
const DEFAULT_LOCALE = 'en'

export function Backdrop({ className, children, ...props }: BackdropProps) {
  return (
    <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }}>
      <div {...props} style={{ backgroundColor: 'rgba(0, 0, 0, 0.67)' }} className={cn('absolute inset-0', className)}>
        {children}
      </div>
    </motion.div>
  )
}

export function BaseCard({ width, children, className, onClose, ...props }: BaseCardProps) {
  const musicRef = useRef(backgroundMusic1)
  const [isMusicOn, setIsMusicOn] = useState(!musicRef.current.paused)

  const handleMusicOn = () => {
    setIsMusicOn(true)
    musicRef.current.play()
  }

  const handleMusicOff = () => {
    setIsMusicOn(false)
    musicRef.current.pause()
  }

  const handleClose = () => {
    handleMusicOff()
    closeCancelAudio.play()
    onClose?.()
  }

  return (
    <motion.div initial={{ opacity: 0, scale: 0 }} animate={{ opacity: 1, scale: 1 }} exit={{ opacity: 0, scale: 0 }}>
      <div
        {...props}
        role="neon-a-base-card"
        style={{ width: `${width}px`, aspectRatio: '375/667' }}
        className={cn('relative', className)}
      >
        {/* content */}
        <div className="absolute inset-0">{children}</div>

        {/* buttons */}
        <div className="absolute inset-x-0 top-2 flex items-center justify-between px-3">
          {isMusicOn ? (
            <Image src={MusicIcon} alt="music-icon" className="w-6" onClick={handleMusicOff} />
          ) : (
            <Image src={MusicOffIcon} alt="music-off-icon" className="w-6" onClick={handleMusicOn} />
          )}
          <Image src={CloseIcon} alt="close-icon" className="w-8" onClick={handleClose} />
        </div>
      </div>
    </motion.div>
  )
}

export function WinCard({
  locale = DEFAULT_LOCALE,
  reward = 0,
  width = DEFAULT_WIDTH,
  onClose,
  onStart,
  onShare,
}: CardProps) {
  return (
    <BaseCard data-title="neon-a-win-card" width={width} onClose={onClose}>
      <TitleWinImage locale={locale} className="w-full" />
      <div className="absolute inset-x-0 bottom-7">
        <div className="relative">
          <Image
            src={BG_REWARD}
            alt="bg-reward"
            className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2"
          />
          <p
            style={{ fontFamily: '-apple-system', fontWeight: 'bold' }}
            className="relative text-center text-7xl leading-none text-[#03FFF6]"
          >
            {formatNumber(reward)}
          </p>
        </div>
        <TextWinImage locale={locale} className="mx-auto mt-4 w-[300px]" />
        <div className={cn('mt-2 flex justify-center', 'invisible')}>
          <BtnStart locale={locale} className="w-[120px]" onClick={onStart} />
          <BtnShare locale={locale} className="w-[120px]" onClick={onShare} />
        </div>
      </div>
    </BaseCard>
  )
}

export function LoseCard({ locale = DEFAULT_LOCALE, width = DEFAULT_WIDTH, onClose, onStart, onShare }: CardProps) {
  return (
    <BaseCard data-title="neon-a-lose-card" width={width} onClose={onClose}>
      <TitleLoseImage locale={locale} className="w-full" />
      <div className="absolute inset-x-0 bottom-7">
        <TextLoseImage locale={locale} className="mx-auto w-[300px]" />
        <div className={cn('mt-2 flex justify-center', 'invisible')}>
          <BtnStart locale={locale} className="w-[120px]" onClick={onStart} />
          <BtnShare locale={locale} className="w-[120px]" onClick={onShare} />
        </div>
      </div>
    </BaseCard>
  )
}
