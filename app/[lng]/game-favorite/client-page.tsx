'use client'

import ContentHeaderBar from '@/components/client/content-header-bar'
import GameShowsList from '@/components/client/game-show-list/game-show-list'
import Favorite from '@/public/svg/mini_game/game_favorite.svg'
import GlobalSearch from '../home/<USER>/home/<USER>'
import { useGameFavorite } from './hooks/useGameFavorite'
import SearchLoad from '@/components/client/load-more'
import { useTranslate } from '@tolgee/react'

export default function GameFavoriteClientPage() {
  const { t } = useTranslate()
  const { gameData, gameList, gameListLoading, pageNum, setPageNum } = useGameFavorite()
  return (
    <div className="m-auto flex max-w-[1200px] flex-col">
      <ContentHeaderBar title={t('silderBar.favorite')} Icon={Favorite} />
      <div className="flex flex-col gap-8 p-8">
        <GlobalSearch />
        <GameShowsList
          gameList={gameList || []}
          isGameListLoading={gameListLoading}
          noDataText={'mini_game.favorite'}
        />
        <SearchLoad
          total={gameData?.total || 0}
          pageSize={gameData?.list.length || 0}
          pageNum={pageNum}
          isGameListLoading={gameListLoading}
          onLoad={setPageNum}
        />
      </div>
    </div>
  )
}
