import { openThirdPartyUrl } from '@/utils/open-page'
import { JumpAction, JumpType } from '@libs/typings'
import { redirect, RedirectType } from 'next/navigation'

export const ROUTES_PARAMS_KEY = {
  REDIRECT: 'redirect', // 跳轉地址
  INVITATIONCODE: 'ic', // 邀請碼
  SIGN: 'sign', // 登錄註冊
}

export const ROUTES_PARAMS_VALUE = {
  NEED_LOGIN: 'sign-in',
  NEED_REGISTER: 'sign-up',
}

export const codeToRouteMapping: Record<string, string> = {
  home: '/home',
  promo: '/promo?events=event',
  wallet: '',
  shareholder: '',
  agent: '/agent/center',
  invite: '/agent/invite',
  notification: '',
  support: '',
  vip: '/promo?events=event_vip',
  tasks: '/promo?events=tasks',
  rebate: '/my-rebate',
  team: '',
  sports: '',
  register: `?${ROUTES_PARAMS_KEY.SIGN}=${ROUTES_PARAMS_VALUE.NEED_REGISTER}`,
  login: `?${ROUTES_PARAMS_KEY.SIGN}=${ROUTES_PARAMS_VALUE.NEED_LOGIN}`,
  profile: '',
  bets: '',
  about_us: '/support/about-us',
  user_agreement: '/support/user-agreement',
  cookie_policy: '/support/cookie-policy',
}

// 控制跳轉行為
export function onJump(jump?: JumpAction, defaultCallback?: (jump?: JumpAction) => void) {
  if (jump) {
    const { jumpType = 'EXTERNAL_LINK' } = jump
    const isJumpType = Object.keys(jumpTypeCallbacks).includes(jumpType)
    if (isJumpType && jumpType !== 'NONE') {
      return jumpTypeCallbacks[jumpType](jump)
    }
  }

  return defaultCallback?.()
}

const jumpTypeCallbacks: Record<Exclude<JumpType, 'NONE' | undefined>, (jump: JumpAction) => void> = {
  EXTERNAL_LINK: (jump) =>
    openThirdPartyUrl({
      type: 'link',
      data: jump.jumpContent,
    }),
  EVENT: () => REDIRECT('/promo?events=event'),
  RECHARGE: () => REDIRECT('/account-assets'),
  REBATE: () => REDIRECT('/my-rebate'),
  INVITATION: () => REDIRECT('/agent/invite'),
  AGENT: () => REDIRECT('/agent/center'),
  VIP: () => REDIRECT('/promo?events=event_vip'),
  GAME: () => REDIRECT('/home'),
  TASK: () => REDIRECT('/promo?events=tasks'), // TODO
  REDENVELOPE: (jump) => REDIRECT(`/promo/red-envelope?id=${jump.jumpContent}`),
  FISSION: (jump) => REDIRECT(`/promo/fission?id=${jump.jumpContent}`),
  REBATE_ACTIVITY: (jump) => REDIRECT(`/promo/rebate?id=${jump.jumpContent}`),
  EMERGENCY: (jump) => REDIRECT(`/promo/emergency?id=${jump.jumpContent}`),
  PHONE: (jump) => REDIRECT(`/promo/phone-vip-invite?id=${jump.jumpContent}`), // TODO
  RANK: (jump) => REDIRECT(`/promo/rank?id=${jump.jumpContent}`),
  CHARACTER_COLLECTION: (jump) => REDIRECT(`/promo/character?id=${jump.jumpContent}`),
  AGENT_SUPPORT_PROGRAM: (jump) => REDIRECT(`/promo/support-program?id=${jump.jumpContent}`),
  DEPOSIT_BONUS: (jump) => REDIRECT(`/promo/deposit?id=${jump.jumpContent}`),
  COUPON: (jump) => REDIRECT(`/promo/coupon?id=${jump.jumpContent}`),
  LUCKY_ROULETTE: (jump) => REDIRECT(`/promo/lucky-roulette?id=${jump.jumpContent}`),
}

function REDIRECT(path: string, type: RedirectType = RedirectType.push) {
  redirect(path, type)
}

// 不需要登入驗證的路由
const allowPath = [
  '/',
  '/home',
  '/ui/**',
  '/mini-game/**',
  '/mini-game/details/**',
  '/support/about-us',
  '/support/user-agreement',
  '/support/cookie-policy',
  '/app-download',
  'mini-game/game-show',
  '/403',
  '/503',
]

const convertPatternToRegex = (pattern: string): RegExp => {
  return new RegExp(
    pattern
      .replace(/\//g, '\\/') // 轉義斜線
      .replace(/\*\*/g, '.*') // ** 轉換為任意字符
      .replace(/\*/g, '[^\\/]*') + // * 轉換為非斜線的任意字符
      '$',
  )
}

export const regexAllowPath = allowPath.map((p) => convertPatternToRegex(p))

export const removeSearchParams = (values: string[]) => {
  const url = new URL(window.location.href)
  values.forEach((item) => url.searchParams.delete(item))
  window.history.replaceState({}, '', url.toString())
}
