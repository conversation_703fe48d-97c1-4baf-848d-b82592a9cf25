'use client'

import Loading from '@/app/loading'
import { createApi } from '@libs/apis/src'
import { transformDataToObject } from '@libs/utils/src'
import { useTranslate } from '@tolgee/react'
import { App, Empty } from 'antd'
import { useSearchParams } from 'next/navigation'
import { useEffect } from 'react'

import useSWRMutation from 'swr/mutation'

export type PageType = 'link' | 'setFiatPayCharge'

export default function ThirdPartyUrl() {
  const { t } = useTranslate()
  const { message } = App.useApp()
  const searchParams = useSearchParams()

  const pageType = searchParams.get('type') || 'link'
  const pageData = searchParams.get('data') || ''

  const {
    data: isSuccess,
    trigger: submitDeposit,
    isMutating: isLoading,
  } = useSWRMutation('submitDeposit', async () => {
    try {
      const searchStr = searchParams.get('data')
      const formData = transformDataToObject(searchStr)

      if (!formData.amount) {
        message.error(t('deposit.enter_amount', {}))
        return
      }

      const { data } = await createApi().apis.finance.setFiatPayCharge({
        data: formData,
      })

      const { url } = data || {}

      if (url) {
        location.href = url
        return true
      }

      return true
    } catch (error) {
      console.error(error)
      return false
    }
  })

  useEffect(() => {
    if (pageType === 'link') {
      location.href = pageData
      return
    }

    if (pageType === 'setFiatPayCharge') {
      submitDeposit()
    }
  }, [pageType, pageData, submitDeposit])

  useEffect(() => {
    if (!isSuccess) {
      setTimeout(() => {
        window.close()
      }, 5 * 1000)
    }
  }, [isSuccess])

  if (isLoading || pageType === 'link') {
    return (
      <div className="flex h-screen w-full items-center justify-center bg-shades-0">
        <Loading />
      </div>
    )
  }

  if (isSuccess) {
    return (
      <div className="flex h-screen w-full items-center justify-center bg-shades-0">
        <Empty description={t('deposit.success', {})} />
      </div>
    )
  }

  return (
    <div className="flex h-screen w-full items-center justify-center bg-shades-0">
      <Empty description={t('deposit.failed', {})} />
    </div>
  )
}
