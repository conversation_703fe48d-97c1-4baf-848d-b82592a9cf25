'use client'

import { getIcon } from '@/utils/getIcon'
import { GameShowHeaderIcon } from '../game-shows/constants'
import ContentHeaderBar from '@/components/client/content-header-bar'
import GlobalSearch from '@/app/[lng]/home/<USER>/home/<USER>'
import { useProviders } from '../details/[gameId]/hooks/useProviders'
import ProviderList from './components/provider-list'
import { useTranslate } from '@tolgee/react'

export default function GameProviderClientPage() {
  const { providerList, isLoading } = useProviders()
  const { t } = useTranslate()
  return (
    <div className="m-auto flex max-w-[1200px] flex-col">
      <ContentHeaderBar
        title={t('mini_game.game_provider', {})}
        Icon={() =>
          getIcon(GameShowHeaderIcon['others'], {
            className: 'h-full w-[180px] object-contain',
          })
        }
      />
      <div className="mt-8 flex flex-col gap-8">
        <GlobalSearch />
        <ProviderList isLoading={isLoading} providerList={providerList} />
      </div>
    </div>
  )
}
