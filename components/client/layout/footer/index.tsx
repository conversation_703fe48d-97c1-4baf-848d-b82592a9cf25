'use client'
import type { FooterType, S3DbJson, UserAgreement } from '@libs/apis'
import FooterLinkGroup from './components/footer-link-group'
import { concatAboutUs, useFooterPath, userAgreementAgeImage } from './hooks/useFooter'
import { useAboutUs } from './hooks/useAboutUs'
import { useVipInfo } from '@/app/[lng]/promo/hooks/vipInfo'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import HtmlDecoder from '../../html'
import { HiddeModule } from '@/store/layout'

type Props = {
  showFooter: boolean
  dbJson: S3DbJson | null
  footerJson: FooterType | null
  userAgreement: UserAgreement | null
  hiddeModule: HiddeModule
}

export default function Footer({ showFooter, dbJson, footerJson, userAgreement, hiddeModule }: Props) {
  const { logo, darkLogo } = dbJson?.config?.brand.image || {}
  const logoImg = darkLogo || logo
  const router = useRouter()
  const { gameList, groupLinkClick } = useFooterPath()
  const ageImage = userAgreement ? userAgreementAgeImage(userAgreement.icon) : null
  const { data } = useAboutUs(footerJson?.linkGroups || [])
  const { vipStatus, isLogin } = useVipInfo()

  if (!showFooter || !footerJson) return null
  const { company, socialMedias, linkGroups } = footerJson
  return (
    <footer className="mt-6 w-[100vw] overflow-hidden px-[16px] pb-32 pt-6 md:mx-auto md:w-[1200px]">
      <div className="flex flex-col justify-between gap-16 xl:flex-row">
        <div className="flex flex-col gap-8 xl:flex-row xl:gap-[6vw]">
          <div className="footer-start min-w-[160px] space-y-4">
            {logoImg && (
              <Image
                className="h-[38px] w-[104x] cursor-pointer object-contain"
                onClick={() => router.push('/home')}
                width={104}
                height={38}
                src={logoImg}
                alt="logo"
              />
            )}
            <HtmlDecoder className="break-words text-xs text-bgs-100 xl:max-w-[320px]" html={company} />
            <div className="footer-end shrink-0">
              <div className="flex items-start gap-3">
                {ageImage && (
                  <Image className="h-[40px] w-[40px] object-contain" src={ageImage} width={32} height={32} alt="age" />
                )}
              </div>
            </div>
          </div>
          <FooterLinkGroup
            linkGroups={concatAboutUs(data || [], linkGroups, hiddeModule, gameList, vipStatus, isLogin)}
            groupLinkClick={groupLinkClick}
            socialMedias={socialMedias}
          />
        </div>
      </div>
    </footer>
  )
}
