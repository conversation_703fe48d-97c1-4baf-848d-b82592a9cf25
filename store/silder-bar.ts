import type { HallTabItem, SliderBarType } from '@libs/apis'
import { create } from 'zustand'

export const useSilderBarStore = create<{
  silder: SliderBarType
  gameList: HallTabItem[]
  showLeftMenu: boolean
  setSilders: (silder: SliderBarType) => void
  setGameList: (gameList: HallTabItem[]) => void
  setLeftMenu: (value: boolean) => void
}>((set) => ({
  silder: {
    menus: [],
  },
  showLeftMenu: false,
  gameList: [],
  setSilders: (silder: SliderBarType) => set({ silder }),
  setGameList: (gameList) => set({ gameList }),
  setLeftMenu: (value: boolean) => set((state) => ({ ...state, showLeftMenu: value })),
}))
