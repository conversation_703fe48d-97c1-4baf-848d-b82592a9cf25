'use client'

import MiniGameContainer from './components/mini-game-container'
import {
  useMiniGameButtons,
  useMiniGameDispatch,
  useMiniGameSettings,
  useMiniSharedMedia,
  useMiniTryLink,
} from './hooks/useMiniGame'
import { useIframe } from './hooks/useIframe'
import { cn } from '@libs/utils/src'
import MiniGameShared from './components/mini-game-shared'
import { useHotGames } from './hooks/useHotGames'
import GameList from '../../../home/<USER>/home/<USER>'
import { useProviders } from './hooks/useProviders'
import { useTranslate } from '@tolgee/react'

export type MiniGameUnLoginProps = {
  lng: string
  gameId?: string
  groupId: string
}

export default function MiniGameUnLoginPage({ gameId, groupId, lng }: MiniGameUnLoginProps) {
  const { t } = useTranslate()
  const { gameDetails, coin, miniGameTryLink, tryLinkLoading, gameDetailsLoading, gameDetailsReload, reloadTryLink } =
    useMiniTryLink({ gameId, lng, groupId })

  const { miniGameButtons } = useMiniGameButtons(gameDetails)
  const { copyUrl, sharedList, openModal, handleModal, openApp } = useMiniSharedMedia(gameId, groupId, lng)
  const { theater, showMiniGameScreen, gameUrl, fullScreen, handleButtonClick, handleSettingClick, setFullScreen } =
    useMiniGameDispatch({
      lng,
      coin,
      miniGameUrl: miniGameTryLink!,
      gameDetails,
      gameDetailsReload,
      handleModal,
      reloadGameLink: reloadTryLink,
    })
  const { miniGameSetting, showHighlight, setShowHighlight } = useMiniGameSettings(theater, gameDetails)
  const { iframePosition, iframeContainerRef, iframeOnLoad } = useIframe(theater, fullScreen, setFullScreen)
  const { hostGameList } = useHotGames()
  const { providerList } = useProviders()

  return (
    <>
      <MiniGameContainer
        showMiniGameScreen={showMiniGameScreen}
        gameDetailsLoading={gameDetailsLoading}
        linkLoading={tryLinkLoading}
        gameDetails={gameDetails}
        miniGameButtons={miniGameButtons}
        theater={theater}
        miniGameSettings={miniGameSetting}
        iframePosition={iframePosition}
        gameUrl={gameUrl}
        iframeContainerRef={iframeContainerRef}
        showHighlight={showHighlight}
        setShowHighlight={setShowHighlight}
        handleButtonClick={handleButtonClick}
        handleSettingClick={handleSettingClick}
        iframeOnLoad={iframeOnLoad}
      />
      <div className={cn('ml-auto mr-auto mt-8', theater ? 'max-w-[1200px]' : '')}>
        <GameList code={'hot'} title={t('mini_game.recom_game', {})} dataList={hostGameList} type="gameList" />
      </div>
      <div className={cn('ml-auto mr-auto mt-8', theater ? 'max-w-[1200px]' : '')}>
        <GameList code={'Baccarat'} title={t('mini_game.game_provider', {})} dataList={providerList} type="providers" />
      </div>
      <MiniGameShared
        copyUrl={copyUrl}
        isModalOpen={openModal}
        sharedList={sharedList}
        handleModal={handleModal}
        openApp={openApp}
      />
    </>
  )
}
