<svg viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g filter="url(#filter0_i_4031_17985)">
    <path
      d="M13.708 2.80389C14.4226 2.80397 15.0018 3.38323 15.002 4.09784V12.0881C14.6619 11.972 14.2973 11.9084 13.918 11.9084C12.0664 11.9085 10.5655 13.4094 10.5654 15.2609C10.5654 15.9151 10.7532 16.5253 11.0771 17.0412H6.58887C5.87433 17.041 5.29512 16.4618 5.29492 15.7473V4.09784C5.29511 3.3833 5.87433 2.80409 6.58887 2.80389H13.708Z"
      fill="#FFC039" />
    <rect x="11.4795" y="12.8222" width="4.87511" height="4.87511" rx="2.43755" fill="#FFC039" />
    <path d="M13.917 14.3098V16.2136" stroke="#131313" stroke-width="0.707609" stroke-linecap="round"
      stroke-linejoin="round" />
    <path d="M14.8686 15.2618H12.9648" stroke="#131313" stroke-width="0.707609" stroke-linecap="round"
      stroke-linejoin="round" />
  </g>
  <defs>
    <filter id="filter0_i_4031_17985" x="0.825195" y="0.25" width="20" height="20" filterUnits="userSpaceOnUse"
      color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix" />
      <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        result="hardAlpha" />
      <feOffset />
      <feGaussianBlur stdDeviation="1.5" />
      <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
      <feColorMatrix type="matrix" values="0 0 0 0 0.984314 0 0 0 0 0.419608 0 0 0 0 0.0705882 0 0 0 0.71 0" />
      <feBlend mode="normal" in2="shape" result="effect1_innerShadow_4031_17985" />
    </filter>
  </defs>
</svg>
