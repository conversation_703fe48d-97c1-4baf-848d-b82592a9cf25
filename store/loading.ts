import { create } from 'zustand'

type LoadingState = {
  isLoading: boolean
}

type LoadingAction = {
  setIsLoading: (loading: boolean) => void
  setLoadingTrue: () => void
  setLoadingFalse: () => void
  reset: () => void
}

const initialState: LoadingState = {
  isLoading: false,
}

export const useLoadingStore = create<LoadingState & LoadingAction>((set) => ({
  ...initialState,
  setIsLoading: (loading: boolean) =>
    set(() => ({
      isLoading: loading,
    })),
  setLoadingTrue: () =>
    set(() => ({
      isLoading: true,
    })),
  setLoadingFalse: () =>
    set(() => ({
      isLoading: false,
    })),
  reset: () => {
    set(initialState)
  },
}))
