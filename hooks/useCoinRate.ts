import useSWR from 'swr'

import { createApi } from '@libs/apis'
import { useAuth } from '@modules/auth'

export default function useCoinRate(symbol?: string) {
  const { isLogin } = useAuth()
  const { data, error, isLoading } = useSWR(`coinRatePath-${isLogin}`, async () => {
    if (!isLogin) {
      return null
    }
    const { data } = await createApi().apis.finance.getCoinRate()
    if (error) throw error
    return data
  })

  return {
    isLoading,
    error,
    data,
    coinRate: data?.coinRate || [],
    fiatCurrencyRate: data?.legalCurrencyRate || [],
    userCurrencyRate: data?.legalCurrencyRate.find((item) => item.currency === symbol)?.rate || 1,
    defaultCoin: data?.coin || '',
  }
}
