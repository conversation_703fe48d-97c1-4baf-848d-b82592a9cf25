import type { Config } from 'tailwindcss'
import daisyui from 'daisyui'
// import plugin from "tailwindcss/plugin"

const customDaisyui = {
  ...daisyui,
  config: {
    themes: false,
  },
}

const getColor = (params: { color: string; length: number; unit?: number }) => {
  const { color, length, unit = 1 } = params
  const res: Record<string, string> = {}
  for (let i = 0; i < length; i++) {
    const key = (i + 1) * unit
    res[`${key}`] = `var(--color-${color}-${key})`
  }
  return res
}

export default {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    './stories/**/*.mdx',
    './stories/**/*.stories.@(js|jsx|mjs|ts|tsx)',
    './stories/**/*.@(js|jsx|mjs|ts|tsx)',
  ],
  important: '#root',
  theme: {
    screens: {
      'xs': '480px',
      'sm': '640px',
      'md': '768px',
      'lg': '1024px',
      'xl': '1280px',
      '2xl': '1536px',
    },
    extend: {
      fontSize: {
        '3xs': ['0.625rem', '1.4rem'],
        '2xs': ['11px', '1.4rem'],
        xs: ['0.75rem', '1.4rem'],
        sm: ['0.875rem', '1.5rem'],
        base: ['1rem', '1.5rem'],
        lg: ['1.125rem', '1.625rem'],
        xl: ['1.25rem', '1.75rem'],
        '2xl': ['1.5rem', '2rem'],
        '3xl': ['1.875rem', '2.325rem'],
        '4xl': ['2.25rem', '3rem'],
        '5xl': ['3rem', '3.5rem'],
        '6xl': ['3.75rem', '4.25rem'],
        '7xl': ['4.5rem', '5rem'],
      },
      animation: {
        glow: 'game-circle 1s infinite ease-out',
      },
      keyframes: {
        'game-circle': {
          '0%': { boxShadow: '0 0 rgba(0, 231, 1, 0.4)' },
          '30%': { boxShadow: '0 0 2px 2px rgba(0, 231, 1, 0.4)' },
          '70%': { boxShadow: '0 0 2px 2px rgba(0, 231, 1, 0.4)' },
          '100%': { boxShadow: '0 0 rgba(0, 231, 1, 0.4)' },
        },
      },
      zIndex: {
        '-10': '-10',
        '-1': '-1',
        '0': '0',
        '1': '1',
        '10': '10',
        '20': '20',
        '30': '30',
        '40': '40',
        '50': '50',
        '60': '60',
        '70': '70',
        '80': '80',
        '90': '90',
        '100': '100',
        auto: 'auto',
      },
      boxShadow: {
        'game-detail':
          '0px 2px 1px 0px rgba(0, 0, 0, 0.1),0px 6px 12px 0px rgba(255, 255, 255, 0.04) inset,0px 1px 1px 0px rgba(255, 255, 255, 0.2) inset',
      },
      colors: {
        black: 'var(--color-black)',
        white: 'var(--color-white)',
        gray: getColor({
          color: 'gray',
          length: 3,
        }),
        primary: getColor({
          color: 'primary',
          length: 4,
        }),
        secondary: getColor({
          color: 'secondary',
          length: 3,
        }),
        gs: getColor({
          color: 'gs',
          length: 9,
          unit: 100,
        }),
        bgs: getColor({
          color: 'bgs',
          length: 9,
          unit: 100,
        }),
        shades: {
          0: 'var(--color-shades-0)',
          100: 'var(--color-shades-100)',
        },
        success: getColor({
          color: 'success',
          length: 10,
        }),
        error: getColor({
          color: 'error',
          length: 9,
        }),
        warning: getColor({
          color: 'warning',
          length: 8,
        }),
        info: getColor({
          color: 'info',
          length: 3,
        }),
        'on-button-primary': 'var(--color-on-button-primary)',
      },
    },
  },
  plugins: [customDaisyui],
} satisfies Config
