import Image from 'next/image'
import { Suspense } from 'react'

import svgFiles from './svg-files.json'

export function BingoSvg() {
  const removePublicPrefix = (path: string) => {
    return path.replace('public/', '') // 移除 public/ 前綴
  }

  const containerStyle = {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
    gap: '16px',
    padding: '16px',
  }

  return (
    <div style={containerStyle}>
      {svgFiles.map((pathName: string) => (
        <SvgIcon key={pathName} pathName={removePublicPrefix(pathName)} />
      ))}
    </div>
  )
}

function SvgIcon({ pathName }: { pathName: string }) {
  const src = pathName

  const containerStyle = {
    display: 'flex',
    alignItems: 'center',
    gap: '4px',
    border: '1px solid #ccc',
    borderRadius: '4px',
  }

  return (
    <Suspense fallback={<div>loading...</div>}>
      <figure style={containerStyle}>
        <Image src={src} width={50} height={50} alt={pathName} />
        <figcaption>{src}</figcaption>
      </figure>
    </Suspense>
  )
}
