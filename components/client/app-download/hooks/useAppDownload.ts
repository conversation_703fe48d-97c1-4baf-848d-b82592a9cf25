import { useLayoutStore } from '@/store/layout'
import { getWindowStorageByKey, setWindowStorageByKey } from '@libs/utils/src'
import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'

const HAS_SEEN_DOWNLOAD_MODAL = 'hasSeenDownloadModal'

export const useAppDownload = () => {
  const { layoutData } = useLayoutStore()
  const storehasClose = getWindowStorageByKey(HAS_SEEN_DOWNLOAD_MODAL)
  const [hasClose, setHasClose] = useState(storehasClose)
  const router = useRouter()
  const pwa = layoutData?.dbJson?.config?.pwa
  const title = pwa?.short_name ?? ''

  const desc = pwa?.popup_description ?? ''

  const logoSrc = pwa?.icons.find((icon) => icon.sizes === '512x512')?.src || ''
  const { components } = layoutData?.dbJson?.page?.home || {}

  const version = components?.find((item) => item.title?.includes('app-download'))?.title?.split(':')?.[2]
  const [height, setHeight] = useState(0)
  const handleClose = () => {
    setWindowStorageByKey(HAS_SEEN_DOWNLOAD_MODAL, true)
    setHasClose(true)
  }

  useEffect(() => {
    const appContent = document.getElementById('app-content')

    if (!appContent) return

    const visibleTop = window.innerHeight - 200
    const handleScroll = (event: Event) => {
      const target = event.target as HTMLElement

      setHeight(target.scrollTop + (version === 'v1' ? 38 : visibleTop))
    }
    setHeight(version === 'v1' ? 38 : visibleTop)

    appContent.addEventListener('scroll', handleScroll)

    return () => {
      appContent.removeEventListener('scroll', handleScroll)
    }
  }, [version])

  return {
    version,
    hasClose,
    router,
    title,
    desc,
    logoSrc,
    height,
    handleClose,
  }
}
