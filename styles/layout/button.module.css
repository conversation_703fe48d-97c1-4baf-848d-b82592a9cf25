:root {
   --transition-duration: 0.15s;
   --transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter;
   --transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
   --transition: var(--transition-property) var(--transition-duration) var(--transition-timing-function);
}

.primaryButton {
  box-sizing: 'border-box';

  /* layout */
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0px;
  padding: 12px;

  /* colors */
  color: rgb(24, 24, 27);
  background-color: rgb(255, 192, 56);

  /* borders */
  border-radius: 12px;

  /* effects */
  box-shadow: rgba(251, 107, 18, 0.71) 0px 0px 12.9px 0px inset, rgb(230, 129, 35) 0px 4px 0px 0px, rgba(0, 0, 0, 0.1) 0px 1px 3px 0px, rgba(0, 0, 0, 0.1) 0px 1px 2px -1px;

  /* animations */
  transition: var(--transition);

  /* display */
  user-select: none;
  cursor: pointer;

  &:hover {
    background-color: rgb(255, 202, 87);
    box-shadow: rgba(251, 107, 18, 0.71) 0px 0px 12.9px 0px inset, rgb(230, 129, 35) 0px 6px 0px 0px, rgba(0, 0, 0, 0.1) 0px 1px 3px 0px, rgba(0, 0, 0, 0.1) 0px 1px 2px -1px;
    transform: translateY(-2px);
  }

  &:active {
    box-shadow: rgba(251, 107, 18, 0.71) 0px 0px 12.9px 0px inset, rgb(230, 129, 35) 0px 0px 0px 0px, rgba(0, 0, 0, 0.1) 0px 1px 3px 0px, rgba(0, 0, 0, 0.1) 0px 1px 2px -1px;
    transform: translateY(2px);
    scale: .98;
  }
}

.primaryButtonBlack {
  box-sizing: 'border-box';

  /* layout */
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0px;
  padding: 12px;

  /* colors */
  color: rgb(255, 255, 255);
  background-color: rgba(28,35,38);

  /* borders */
  border-radius: 12px;

  /* effects */
  box-shadow: rgba(46, 62, 71, 0) 0px 0px 12.9px 0px inset, rgb(36, 43, 48) 0px 4px 0px 0px, rgba(0, 0, 0, 0.1) 0px 1px 3px 0px, rgba(0, 0, 0, 0.1) 0px 1px 2px -1px;

  /* animations */
  transition: var(--transition);

  /* display */
  user-select: none;
  cursor: pointer;

  &:hover {
    background-color: rgb(46, 62, 71);
    box-shadow: rgba(46, 62, 71, 0) 0px 0px 12.9px 0px inset, rgb(36, 43, 48) 0px 6px 0px 0px, rgba(0, 0, 0, 0.1) 0px 1px 3px 0px, rgba(0, 0, 0, 0.1) 0px 1px 2px -1px;
    transform: translateY(-2px);
  }

  &:active {
    box-shadow: rgba(46, 62, 71, 0) 0px 0px 12.9px 0px inset, rgb(36, 43, 48) 0px 0px 0px 0px, rgba(0, 0, 0, 0.1) 0px 1px 3px 0px, rgba(0, 0, 0, 0.1) 0px 1px 2px -1px;
    transform: translateY(2px);
    scale: .98;
  }
}

.secondaryButton {
    box-sizing: 'border-box';

    /* layout */
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0px;
    padding: 12px;

    /* colors */
    color: rgb(255, 255, 255);
    background-color: rgba(0, 0, 0, 0);

    /* borders */
    border-radius: 12px;

    /* animations */
    transition: var(--transition);

    /* display */
    user-select: none;
    cursor: pointer;

    &:hover {
        color: rgb(250, 250, 250);
        background-color: rgb(36, 48, 56);
    }

    &:active {
      scale: .98;
    }
}
